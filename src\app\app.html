<!-- <PERSON>ed<PERSON> AI - Podcast to YouTube Description Generator -->
<div class="min-h-screen bg-gray-50">
  <!-- Top Navigation Bar -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo and Title -->
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h1 class="text-xl font-semibold text-gray-900">Pedma AI</h1>
          <span class="text-sm text-gray-500">Podcast to YouTube Description</span>
        </div>

        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- Dark Mode Toggle -->
          <button 
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            (click)="toggleDarkMode()"
            title="Toggle dark mode">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
            </svg>
          </button>

          <!-- User Profile -->
          <div class="relative" *ngIf="user$ | async as user; else loginButton">
            <button 
              class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              (click)="toggleUserMenu()">
              <img 
                [src]="user.photoURL || '/assets/default-avatar.png'" 
                [alt]="user.displayName || 'User'"
                class="w-8 h-8 rounded-full">
              <span class="text-sm font-medium text-gray-700">{{ user.displayName || user.email }}</span>
            </button>
            
            <!-- User Dropdown Menu -->
            <div 
              *ngIf="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">API Keys</a>
              <hr class="my-1">
              <button 
                (click)="signOut()"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Sign Out
              </button>
            </div>
          </div>

          <!-- Login Button -->
          <ng-template #loginButton>
            <button 
              (click)="signInWithGoogle()"
              class="btn-primary">
              Sign In with Google
            </button>
          </ng-template>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content Area -->
  <div class="flex h-[calc(100vh-4rem)]" *ngIf="user$ | async; else welcomeScreen">
    <!-- Left Panel - Chat Interface -->
    <div class="w-2/5 bg-white border-r border-gray-200">
      <!-- Welcome Message (when no session) -->
      <div *ngIf="!currentSession" class="p-6 text-center border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Ready to Start!</h3>
        <p class="text-sm text-gray-600 mb-4">You can start chatting immediately or upload files for processing.</p>
        <div class="flex space-x-3 justify-center">
          <button (click)="createChatSession()" class="btn-primary text-sm px-4 py-2">
            Start Chat Now
          </button>
          <button (click)="openUploadModal()" class="btn-secondary text-sm px-4 py-2">
            Upload Files
          </button>
        </div>
      </div>

      <app-chat-panel
        [messages]="chatMessages"
        [isTyping]="isProcessing"
        [autoMode]="autoMode"
        [processingProgress]="processingProgress"
        [estimatedTimeRemaining]="estimatedTimeRemaining"
        [disabled]="!currentSession"
        (messageSent)="sendMessage($event)"
        (autoModeChanged)="onAutoModeChanged($event)"
        (quickActionSelected)="sendMessage($event)"
        (fileUploaded)="onChatFileUploaded($event)"
        (transcriptPasted)="onTranscriptPasted($event)">
      </app-chat-panel>
    </div>

    <!-- Right Panel - Description Editor -->
    <div class="flex-1 bg-gray-50">
      <app-description-editor
        [description]="currentDescription"
        [isLoading]="isProcessing"
        (copyRequested)="onDescriptionCopied()"
        (exportRequested)="onDescriptionExported($event)">
      </app-description-editor>
    </div>




  </div>

  <!-- Error Notifications -->
  <app-error-notifications></app-error-notifications>

  <!-- Welcome Screen for Non-Authenticated Users -->
  <ng-template #welcomeScreen>
    <div class="flex items-center justify-center min-h-[calc(100vh-4rem)]">
      <div class="text-center max-w-md mx-auto px-4">
        <div class="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Pedma AI</h2>
        <p class="text-gray-600 mb-8">Transform your podcast audio into compelling YouTube descriptions with AI-powered assistance.</p>
        <button 
          (click)="signInWithGoogle()"
          class="btn-primary text-lg px-8 py-3">
          Get Started with Google
        </button>
      </div>
    </div>
  </ng-template>
</div>

<!-- Upload Modal -->
<div *ngIf="showUploadModal"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
     (click)="closeUploadModal()">
  <div class="bg-white rounded-xl p-6 max-w-lg w-full mx-4" (click)="$event.stopPropagation()">
    <h3 class="text-lg font-semibold text-gray-900 mb-6">Upload Content</h3>

    <!-- Upload Zone Component -->
    <app-upload-zone
      [uploadType]="uploadType"
      [disabled]="isUploading"
      (fileSelected)="onFileSelected($event)"
      (uploadTypeChanged)="onUploadTypeChanged($event)">
    </app-upload-zone>

    <!-- Upload Error -->
    <div *ngIf="uploadError"
         class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-start space-x-2">
        <svg class="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
        <div>
          <p class="text-sm font-medium text-red-800">Upload Failed</p>
          <p class="text-sm text-red-700">{{ uploadError }}</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex space-x-3">
      <button
        (click)="closeUploadModal()"
        [disabled]="isUploading"
        class="btn-secondary">
        Cancel
      </button>
      <button
        (click)="startChatWithoutUpload()"
        [disabled]="isUploading"
        class="btn-secondary">
        Start Chat
      </button>
      <button
        (click)="uploadFile()"
        [disabled]="!selectedFile || isUploading"
        class="btn-primary">
        <span *ngIf="!isUploading">Upload {{ uploadType === 'audio' ? 'Audio' : 'Transcript' }}</span>
        <span *ngIf="isUploading">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Uploading...
        </span>
      </button>
    </div>
  </div>
</div>
