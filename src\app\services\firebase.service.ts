import { Injectable, inject, Injector, runInInjectionContext } from '@angular/core';
import {
  Auth,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  user,
  User as FirebaseUser
} from '@angular/fire/auth';
import {
  Firestore,
  doc,
  setDoc,
  getDoc,
  updateDoc,
  collection,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  getDocs
} from '@angular/fire/firestore';
import {
  Storage,
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject
} from '@angular/fire/storage';
import { Observable, from, map, switchMap, of } from 'rxjs';
import { User, ProcessingSession, UserAPIKeys, UserPreferences } from '../models/podcast-description.model';

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private auth = inject(Auth);
  private firestore = inject(Firestore);
  private storage = inject(Storage);
  private injector = inject(Injector);

  // Auth state observable
  user$ = user(this.auth);

  constructor() {}

  // Authentication methods
  signInWithGoogle(): Observable<FirebaseUser | null> {
    const provider = new GoogleAuthProvider();
    return from(signInWithPopup(this.auth, provider)).pipe(
      switchMap((result) => {
        // Create or update user document
        if (result.user) {
          return from(this.createOrUpdateUser(result.user)).pipe(
            map(() => result.user)
          );
        }
        return of(result.user);
      }),
      map(user => user || null)
    );
  }

  async signOut(): Promise<void> {
    try {
      await signOut(this.auth);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  // User management
  private async createOrUpdateUser(firebaseUser: FirebaseUser): Promise<void> {
    const userRef = doc(this.firestore, 'users', firebaseUser.uid);
    const userDoc = await getDoc(userRef);

    const userData: Partial<User> = {
      uid: firebaseUser.uid,
      email: firebaseUser.email || '',
      displayName: firebaseUser.displayName || '',
      photoURL: firebaseUser.photoURL || '',
      lastLoginAt: new Date()
    };

    if (!userDoc.exists()) {
      // Create new user
      const newUser: User = {
        ...userData as User,
        apiKeys: {},
        preferences: {
          theme: 'auto',
          autoMode: false,
          notifications: {
            email: true,
            browser: true
          }
        },
        createdAt: new Date()
      };
      await setDoc(userRef, newUser);
    } else {
      // Update existing user
      await updateDoc(userRef, userData);
    }
  }

  // Get user data
  getUserData(uid: string): Observable<User | null> {
    const userRef = doc(this.firestore, 'users', uid);
    return from(getDoc(userRef)).pipe(
      map(doc => doc.exists() ? doc.data() as User : null)
    );
  }

  // Update user API keys
  async updateUserAPIKeys(uid: string, apiKeys: UserAPIKeys): Promise<void> {
    const userRef = doc(this.firestore, 'users', uid);
    await updateDoc(userRef, { apiKeys });
  }

  // Update user preferences
  async updateUserPreferences(uid: string, preferences: UserPreferences): Promise<void> {
    const userRef = doc(this.firestore, 'users', uid);
    await updateDoc(userRef, { preferences });
  }

  // Session management
  async createSession(session: Omit<ProcessingSession, 'id'>): Promise<string> {
    return runInInjectionContext(this.injector, async () => {
      const sessionsRef = collection(this.firestore, 'sessions');
      const docRef = await addDoc(sessionsRef, session);
      return docRef.id;
    });
  }

  async updateSession(sessionId: string, updates: Partial<ProcessingSession>): Promise<void> {
    const sessionRef = doc(this.firestore, 'sessions', sessionId);
    await updateDoc(sessionRef, { ...updates, updatedAt: new Date() });
  }

  getSession(sessionId: string): Observable<ProcessingSession | null> {
    const sessionRef = doc(this.firestore, 'sessions', sessionId);
    return from(getDoc(sessionRef)).pipe(
      map(doc => doc.exists() ? { id: doc.id, ...doc.data() } as ProcessingSession : null)
    );
  }

  getUserSessions(uid: string, limitCount: number = 10): Observable<ProcessingSession[]> {
    const sessionsRef = collection(this.firestore, 'sessions');
    const q = query(
      sessionsRef,
      where('userId', '==', uid),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    
    return from(getDocs(q)).pipe(
      map(snapshot => 
        snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as ProcessingSession))
      )
    );
  }

  // File storage with unified paths
  async uploadAudioFile(file: File, sessionId: string, uid: string): Promise<string> {
    return runInInjectionContext(this.injector, async () => {
      // Verify user is authenticated
      const currentUser = this.auth.currentUser;
      if (!currentUser || currentUser.uid !== uid) {
        throw new Error('User not authenticated or UID mismatch');
      }

      const timestamp = Date.now();
      const ext = file.name.split('.').pop() || 'bin';
      const fileName = `audio_${timestamp}_${file.name}`;
      const filePath = `files/${uid}/${sessionId}/${fileName}`;
      const fileRef = ref(this.storage, filePath);

      console.log('Uploading audio file to:', filePath);
      await uploadBytes(fileRef, file);
      return await getDownloadURL(fileRef);
    });
  }

  async uploadTranscriptFile(file: File, sessionId: string, uid: string): Promise<string> {
    return runInInjectionContext(this.injector, async () => {
      try {
        // Verify user is authenticated
        const currentUser = this.auth.currentUser;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        if (currentUser.uid !== uid) {
          throw new Error('User ID mismatch');
        }

        // Wait for auth token to be ready
        const token = await currentUser.getIdToken();
        console.log('User authenticated with token:', token ? 'Present' : 'Missing');

        const timestamp = Date.now();
        const ext = file.name.split('.').pop() || 'txt';
        const fileName = `transcript_${timestamp}_${file.name}`;
        const filePath = `files/${uid}/${sessionId}/${fileName}`;

        console.log('Attempting upload to path:', filePath);
        console.log('File details:', { name: file.name, size: file.size, type: file.type });

        const fileRef = ref(this.storage, filePath);

        // Add metadata
        const metadata = {
          contentType: file.type || 'text/plain',
          customMetadata: {
            sessionId: sessionId,
            userId: uid,
            uploadedAt: new Date().toISOString()
          }
        };

        await uploadBytes(fileRef, file, metadata);
        const downloadURL = await getDownloadURL(fileRef);

        console.log('Upload successful, download URL:', downloadURL);
        return downloadURL;

      } catch (error) {
        console.error('Upload error details:', error);
        if (error instanceof Error) {
          throw new Error(`Upload failed: ${error.message}`);
        }
        throw new Error('Upload failed with unknown error');
      }
    });
  }

  async deleteAudioFile(filePath: string): Promise<void> {
    const fileRef = ref(this.storage, filePath);
    await deleteObject(fileRef);
  }

  // Utility methods
  getCurrentUser(): FirebaseUser | null {
    return this.auth.currentUser;
  }

  isAuthenticated(): boolean {
    return !!this.auth.currentUser;
  }

  /**
   * Handle Firebase errors and convert them to user-friendly messages
   */
  private handleFirebaseError(error: any, operation: string): Error {
    let message = `Failed to ${operation}`;

    if (error?.code) {
      switch (error.code) {
        case 'storage/unauthorized':
          message = 'You do not have permission to perform this action';
          break;
        case 'storage/canceled':
          message = 'Upload was canceled';
          break;
        case 'storage/unknown':
          message = 'An unknown error occurred';
          break;
        case 'storage/object-not-found':
          message = 'File not found';
          break;
        case 'storage/bucket-not-found':
          message = 'Storage bucket not found';
          break;
        case 'storage/project-not-found':
          message = 'Project not found';
          break;
        case 'storage/quota-exceeded':
          message = 'Storage quota exceeded';
          break;
        case 'storage/unauthenticated':
          message = 'User is not authenticated';
          break;
        case 'storage/retry-limit-exceeded':
          message = 'Maximum retry limit exceeded';
          break;
        case 'firestore/permission-denied':
          message = 'Permission denied';
          break;
        case 'firestore/unavailable':
          message = 'Service temporarily unavailable';
          break;
        case 'auth/user-not-found':
          message = 'User account not found';
          break;
        case 'auth/network-request-failed':
          message = 'Network error occurred';
          break;
        default:
          message = error.message || message;
      }
    } else if (error?.message) {
      message = error.message;
    }

    return new Error(message);
  }
}
