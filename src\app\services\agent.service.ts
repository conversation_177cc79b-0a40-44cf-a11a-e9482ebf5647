import { Injectable, inject } from '@angular/core';
import { Auth } from '@angular/fire/auth';
import { Observable, from, BehaviorSubject, interval } from 'rxjs';
import { switchMap, takeWhile, map } from 'rxjs/operators';
import { AgentStatus, ProcessingSession, ChatMessage } from '../models/podcast-description.model';

export interface ProcessingRequest {
  sessionId: string;
  audioFileUrl: string;
  userId: string;
  autoMode?: boolean;
}

export interface ChatRequest {
  sessionId: string;
  message: string;
  userId: string;
}

export interface SessionStatusResponse {
  session: ProcessingSession;
  agentContext: any;
  agentStatuses: Record<string, AgentStatus>;
}

@Injectable({
  providedIn: 'root'
})
export class AgentService {
  private auth = inject(Auth);
  private readonly API_BASE_URL = '/api'; // Vercel API routes
  
  // Observables for real-time updates
  private agentStatusSubject = new BehaviorSubject<Record<string, AgentStatus>>({});
  public agentStatus$ = this.agentStatusSubject.asObservable();

  private processingStatusSubject = new BehaviorSubject<string>('idle');
  public processingStatus$ = this.processingStatusSubject.asObservable();

  constructor() {}

  /**
   * Get authorization headers for API calls
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    const token = await user.getIdToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  /**
   * Make API call to Vercel functions
   */
  private async makeApiCall(endpoint: string, data: any): Promise<any> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Start processing an audio file (Local mock for now)
   */
  startProcessing(request: ProcessingRequest): Observable<any> {
    // Mock processing since backend is not available
    return new Observable(observer => {
      this.processingStatusSubject.next('processing');

      setTimeout(() => {
        const mockResponse = {
          success: true,
          data: {
            sessionId: request.sessionId,
            message: 'Processing started in demo mode. Full AI processing requires API key configuration.',
            status: 'processing'
          }
        };

        // Simulate completion after a delay
        setTimeout(() => {
          this.processingStatusSubject.next('complete');
        }, 3000);

        observer.next(mockResponse);
        observer.complete();
      }, 500);
    });
  }

  /**
   * Send a chat message to the AI agent (Local processing for now)
   */
  sendChatMessage(request: ChatRequest): Observable<any> {
    // For now, return a mock response since backend APIs are not available
    return new Observable(observer => {
      setTimeout(() => {
        const mockResponse = {
          success: true,
          data: {
            sessionId: request.sessionId,
            message: this.generateMockResponse(request.message),
            success: true,
            nextAgent: null,
            shouldAskUser: false,
            chatHistory: [],
            agentContext: {
              sessionId: request.sessionId,
              userId: request.userId,
              memoriesCount: 0,
              agentStatusesCount: 0
            },
            workflowStatus: 'ready'
          }
        };
        observer.next(mockResponse);
        observer.complete();
      }, 1000); // Simulate API delay
    });
  }

  private generateMockResponse(userMessage: string): string {
    const message = userMessage.toLowerCase();

    if (message.includes('transcript') || message.includes('content')) {
      return "I can see you've shared some content with me. I'm currently running in demo mode without full AI processing capabilities. To enable full AI features, you'll need to configure API keys for Groq and other services. For now, I can help you with basic chat functionality and file management.";
    }

    if (message.includes('hello') || message.includes('hi')) {
      return "Hello! I'm your AI assistant for creating YouTube descriptions. I'm currently in demo mode. You can upload transcript files or paste content, and I'll help you work with it. To enable full AI processing, please configure your API keys in the settings.";
    }

    if (message.includes('help')) {
      return "I can help you create YouTube descriptions from podcast content. Here's what you can do:\n\n1. Upload audio files or transcript files\n2. Paste transcript content directly in chat\n3. Chat with me about your content\n\nNote: I'm currently in demo mode. For full AI processing, configure your API keys.";
    }

    return "I understand you're asking about: \"" + userMessage + "\". I'm currently in demo mode without full AI capabilities. I can help with basic file management and chat, but for advanced AI processing of your content, you'll need to configure API keys for services like Groq and Deepgram.";
  }

  /**
   * Get current session status
   */
  getSessionStatus(sessionId: string): Observable<SessionStatusResponse> {
    return from(this.makeApiCallGet(`/session-status?sessionId=${sessionId}`)).pipe(
      map((result: any) => {
        if (result.success) {
          return result.data;
        } else {
          throw new Error(result.error || 'Failed to get session status');
        }
      })
    );
  }

  /**
   * Continue auto mode processing
   */
  continueAutoMode(sessionId: string): Observable<any> {
    return from(this.makeApiCall('/continue-auto', { sessionId })).pipe(
      map((result: any) => {
        if (result.success) {
          return result.data;
        } else {
          throw new Error(result.error || 'Failed to continue auto mode');
        }
      })
    );
  }

  /**
   * Make GET API call to Vercel functions
   */
  private async makeApiCallGet(endpoint: string): Promise<any> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`${this.API_BASE_URL}${endpoint}`, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Start polling for status updates
   */
  private startStatusPolling(sessionId: string): void {
    interval(3000) // Poll every 3 seconds
      .pipe(
        switchMap(() => this.getSessionStatus(sessionId)),
        takeWhile((status) => {
          // Continue polling while processing
          const isProcessing = Object.values(status.agentStatuses || {})
            .some(agent => agent.status === 'processing');
          
          if (!isProcessing) {
            this.processingStatusSubject.next('complete');
          }
          
          return isProcessing;
        })
      )
      .subscribe({
        next: (status) => {
          this.agentStatusSubject.next(status.agentStatuses || {});
        },
        error: (error) => {
          console.error('Status polling error:', error);
          this.processingStatusSubject.next('error');
        }
      });
  }

  /**
   * Get current agent statuses
   */
  getCurrentAgentStatuses(): Record<string, AgentStatus> {
    return this.agentStatusSubject.value;
  }

  /**
   * Get processing status
   */
  getCurrentProcessingStatus(): string {
    return this.processingStatusSubject.value;
  }

  /**
   * Reset service state
   */
  reset(): void {
    this.agentStatusSubject.next({});
    this.processingStatusSubject.next('idle');
  }

  /**
   * Get agent progress summary
   */
  getProgressSummary(): Observable<any> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const agents = Object.values(statuses);
        const totalAgents = agents.length;
        const completedAgents = agents.filter(agent => agent.status === 'complete').length;
        const processingAgents = agents.filter(agent => agent.status === 'processing');
        const errorAgents = agents.filter(agent => agent.status === 'error');

        const overallProgress = totalAgents > 0 ? (completedAgents / totalAgents) * 100 : 0;

        return {
          overallProgress,
          totalAgents,
          completedAgents,
          processingAgents: processingAgents.map(agent => ({
            name: agent.name,
            progress: agent.progress,
            message: agent.message
          })),
          errorAgents: errorAgents.map(agent => ({
            name: agent.name,
            message: agent.message
          })),
          isComplete: completedAgents === totalAgents && totalAgents > 0,
          hasErrors: errorAgents.length > 0
        };
      })
    );
  }

  /**
   * Retry failed agent
   */
  retryAgent(sessionId: string, agentName: string): Observable<any> {
    // This would trigger a specific agent retry
    // For now, we'll restart the entire process
    return this.continueAutoMode(sessionId);
  }

  /**
   * Check if processing is complete
   */
  isProcessingComplete(): Observable<boolean> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const agents = Object.values(statuses);
        if (agents.length === 0) return false;
        
        return agents.every(agent => 
          agent.status === 'complete' || agent.status === 'error'
        );
      })
    );
  }

  /**
   * Get estimated time remaining
   */
  getEstimatedTimeRemaining(): Observable<number> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const processingAgents = Object.values(statuses)
          .filter(agent => agent.status === 'processing');
        
        if (processingAgents.length === 0) return 0;

        // Simple estimation based on current progress
        const avgProgress = processingAgents.reduce((sum, agent) => sum + agent.progress, 0) / processingAgents.length;
        const remainingProgress = 100 - avgProgress;
        
        // Estimate 30 seconds per 10% progress
        return Math.round((remainingProgress / 10) * 30);
      })
    );
  }
}
