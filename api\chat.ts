import { NextApiRequest, NextApiResponse } from 'next';
import {
  verifyAuth,
  verifyOwnership,
  getFirebaseAdmin
} from './lib/utils/auth';
import { MemoryManager } from './lib/utils/memory-manager';
import { CoordinatorAgent } from './lib/agents/coordinator';
import { ChatRequest, ValidationError } from './lib/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.status(200).end();
    return;
  }

  const origin = req.headers.origin as string;

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Verify authentication
    const authContext = await verifyAuth(req);

    // Validate request body
    if (!req.body || typeof req.body !== 'object') {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(400).json({ error: 'Invalid request body' });
    }

    const { sessionId, message, userId } = req.body as ChatRequest;

    if (!sessionId || !message || !userId) {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(400).json({ error: 'Missing required fields: sessionId, message, userId' });
    }

    // Verify user owns this session
    verifyOwnership(authContext, userId);

    // Get Firebase admin instances
    const { firestore } = getFirebaseAdmin();

    // Verify session exists and user owns it
    const sessionDoc = await firestore
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      throw new ValidationError('Session not found');
    }

    const sessionData = sessionDoc.data();
    if (!sessionData) {
      throw new ValidationError('Session data not found');
    }

    // Verify ownership again from session data
    verifyOwnership(authContext, sessionData.userId);

    // Get or create agent context
    let agentContext = MemoryManager.getContext(sessionId);
    if (!agentContext) {
      agentContext = MemoryManager.createInitialContext(sessionId, userId);
    }

    // Store the user message in memory
    const chatHistory = MemoryManager.getMemory(sessionId, 'chatHistory') || [];
    chatHistory.push({
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date(),
      type: 'text'
    });
    MemoryManager.addMemory(sessionId, 'chatHistory', chatHistory);

    // Update session status
    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update({
        status: 'processing',
        updatedAt: new Date(),
        chatHistory: chatHistory
      });

    // Process the message through the coordinator
    const coordinator = CoordinatorAgent.getInstance();
    const result = await coordinator.orchestrateWorkflow(sessionId, message, false);

    // Create AI response message
    const aiResponse = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: result.message,
      timestamp: new Date(),
      type: 'text'
    };

    // Add AI response to chat history
    chatHistory.push(aiResponse);
    MemoryManager.addMemory(sessionId, 'chatHistory', chatHistory);

    // Update session with results
    const updateData: any = {
      status: result.success ? 'ready' : 'error',
      updatedAt: new Date(),
      chatHistory: chatHistory
    };

    if (result.updatedDescription) {
      updateData.description = result.updatedDescription;
      MemoryManager.addMemory(sessionId, 'currentDescription', result.updatedDescription);
    }

    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update(updateData);

    // Prepare response data
    const responseData = {
      sessionId,
      message: result.message,
      success: result.success,
      nextAgent: result.nextAgent,
      shouldAskUser: result.shouldAskUser,
      chatHistory: chatHistory,
      agentContext: {
        sessionId: agentContext.sessionId,
        userId: agentContext.userId,
        memoriesCount: Object.keys(agentContext.memories).length,
        agentStatusesCount: Object.keys(agentContext.agentStatuses).length,
      },
      workflowStatus: coordinator.getWorkflowStatus(sessionId),
    };

    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    return res.status(200).json({
      success: result.success,
      data: responseData,
      message: result.message
    });

  } catch (error) {
    console.error('Chat error:', error);

    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    if (error instanceof Error && 'statusCode' in error) {
      return res.status((error as any).statusCode || 400).json({
        error: error.message
      });
    }

    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Chat processing failed'
    });
  }
}
