// Shared types for Vercel API functions
export interface ProcessingRequest {
  sessionId: string;
  audioFileUrl: string;
  userId: string;
  autoMode?: boolean;
}

export interface ChatRequest {
  sessionId: string;
  message: string;
  userId: string;
}

export interface SessionStatusRequest {
  sessionId: string;
}

export interface ContinueAutoRequest {
  sessionId: string;
}

// Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ProcessingResponse {
  sessionId: string;
  status: string;
  message: string;
  updatedDescription?: any;
}

export interface ChatResponse {
  message: string;
  updatedDescription?: any;
  sessionId: string;
}

export interface SessionStatusResponse {
  session: any;
  agentContext: any;
  agentStatuses: Record<string, any>;
}

// Agent status types
export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'thinking' | 'working' | 'completed' | 'error';
  progress: number;
  message: string;
  lastUpdated: Date;
  error?: string;
}

// Memory context types
export interface AgentContext {
  sessionId: string;
  userId: string;
  memories: Record<string, any>;
  agentStatuses: Record<string, AgentStatus>;
  createdAt: Date;
  lastAccessed: Date;
}

// Error types
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'UNAUTHENTICATED');
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = 'Permission denied') {
    super(message, 403, 'PERMISSION_DENIED');
  }
}

export class ValidationError extends ApiError {
  constructor(message: string = 'Invalid request data') {
    super(message, 400, 'INVALID_ARGUMENT');
  }
}
