import { AgentContext, AgentStatus } from '../types';
import { LangChainConfig, AGENT_PROMPTS, SYSTEM_MESSAGES } from '../utils/langchain-config';
import { MemoryManager } from '../utils/memory-manager';

export interface AgentResponse {
  success: boolean;
  message: string;
  nextAgent?: string;
  shouldAskUser?: boolean;
  parameters?: any;
  updatedDescription?: any;
}

export class CoordinatorAgent {
  private static instance: CoordinatorAgent;

  static getInstance(): CoordinatorAgent {
    if (!this.instance) {
      this.instance = new CoordinatorAgent();
    }
    return this.instance;
  }

  async orchestrateWorkflow(
    sessionId: string,
    userInput?: string,
    autoMode: boolean = false
  ): Promise<AgentResponse> {
    const startTime = new Date();
    
    try {
      // Update agent status
      const status: AgentStatus = {
        id: 'coordinator',
        name: 'Coordinator',
        status: 'working',
        progress: 0,
        message: 'Analyzing workflow requirements...',
        lastUpdated: startTime
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);

      const context = MemoryManager.getContext(sessionId);
      if (!context) {
        throw new Error(`No context found for session ${sessionId}`);
      }

      // Update progress
      status.progress = 25;
      status.message = 'Determining next steps...';
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);

      // Get current state
      const transcript = MemoryManager.getMemory(sessionId, 'transcript');
      const currentDescription = MemoryManager.getMemory(sessionId, 'currentDescription') || {};
      
      // Determine next action based on current state
      let nextAction = this.determineNextAction(context, transcript, currentDescription, userInput, autoMode);

      // Update progress
      status.progress = 75;
      status.message = `Decided to run: ${nextAction.nextAgent}`;
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);

      // If we have a user input and it's an editing request, handle it directly
      if (userInput && this.isEditingRequest(userInput)) {
        nextAction = {
          nextAgent: 'editor',
          reasoning: 'User requested description editing',
          parameters: { userRequest: userInput },
          shouldAskUser: false
        };
      }

      // Complete status
      status.status = 'completed';
      status.progress = 100;
      status.message = `Workflow orchestrated: ${nextAction.nextAgent}`;
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);

      return {
        success: true,
        message: nextAction.reasoning,
        nextAgent: nextAction.nextAgent,
        shouldAskUser: nextAction.shouldAskUser,
        parameters: nextAction.parameters
      };

    } catch (error) {
      console.error('Coordinator error:', error);
      
      // Update error status
      const errorStatus: AgentStatus = {
        id: 'coordinator',
        name: 'Coordinator',
        status: 'error',
        progress: 0,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        lastUpdated: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', errorStatus);

      return {
        success: false,
        message: `Coordination failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private determineNextAction(
    context: AgentContext,
    transcript: string,
    currentDescription: any,
    userInput?: string,
    autoMode: boolean = false
  ): any {
    // If no transcript, we need to start with transcription
    if (!transcript) {
      return {
        nextAgent: 'transcriber',
        reasoning: 'No transcript available, need to process audio first',
        parameters: {},
        shouldAskUser: false
      };
    }

    // If we have transcript but no description, start the description process
    if (!currentDescription || Object.keys(currentDescription).length === 0) {
      return {
        nextAgent: 'topic-extractor',
        reasoning: 'Transcript available, starting topic extraction for description',
        parameters: { transcript },
        shouldAskUser: false
      };
    }

    // If we have a basic description but it's incomplete, continue building it
    if (!currentDescription.overview || !currentDescription.seo) {
      return {
        nextAgent: 'description-writer',
        reasoning: 'Description partially complete, finishing description generation',
        parameters: { transcript, currentDescription },
        shouldAskUser: false
      };
    }

    // If user provided input, handle it
    if (userInput) {
      if (this.isEditingRequest(userInput)) {
        return {
          nextAgent: 'editor',
          reasoning: 'User requested description editing',
          parameters: { userRequest: userInput, currentDescription },
          shouldAskUser: false
        };
      }
    }

    // If in auto mode, continue with enhancements
    if (autoMode) {
      return {
        nextAgent: 'reviewer',
        reasoning: 'Auto mode: reviewing and enhancing description',
        parameters: { currentDescription },
        shouldAskUser: false
      };
    }

    // Default: ask user what they want to do
    return {
      nextAgent: 'none',
      reasoning: 'Description complete, waiting for user input',
      parameters: {},
      shouldAskUser: true
    };
  }

  private isEditingRequest(input: string): boolean {
    const editingKeywords = [
      'edit', 'change', 'modify', 'update', 'improve', 'make it', 'add', 'remove',
      'shorter', 'longer', 'more', 'less', 'better', 'different', 'rewrite'
    ];
    
    const lowerInput = input.toLowerCase();
    return editingKeywords.some(keyword => lowerInput.includes(keyword));
  }

  async continueAutoMode(sessionId: string): Promise<AgentResponse> {
    try {
      const context = MemoryManager.getContext(sessionId);
      if (!context) {
        throw new Error(`No context found for session ${sessionId}`);
      }

      // Continue with auto mode
      return await this.orchestrateWorkflow(sessionId, undefined, true);
    } catch (error) {
      console.error('Continue auto mode error:', error);
      return {
        success: false,
        message: `Auto mode continuation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Get workflow status
  getWorkflowStatus(sessionId: string): any {
    const context = MemoryManager.getContext(sessionId);
    if (!context) {
      return null;
    }

    const agentStatuses = context.agentStatuses;
    const completedAgents = Object.values(agentStatuses).filter(
      status => status.status === 'completed'
    ).length;
    
    const totalAgents = Object.keys(agentStatuses).length;
    const overallProgress = totalAgents > 0 ? (completedAgents / totalAgents) * 100 : 0;

    return {
      sessionId,
      overallProgress: Math.round(overallProgress),
      completedAgents,
      totalAgents,
      agentStatuses,
      lastActivity: Math.max(
        ...Object.values(agentStatuses).map(status => status.lastUpdated.getTime())
      )
    };
  }
}
