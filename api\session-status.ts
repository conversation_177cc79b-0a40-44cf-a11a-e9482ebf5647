import { NextApiRequest, NextApiResponse } from 'next';
import {
  verifyAuth,
  verifyOwnership,
  getFirebaseAdmin
} from './lib/utils/auth';
import { MemoryManager } from './lib/utils/memory-manager';
import { ValidationError } from './lib/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.status(200).end();
    return;
  }

  const origin = req.headers.origin as string;

  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Verify authentication
    const authContext = await verifyAuth(req);

    // Get sessionId from query parameters
    const sessionId = req.query.sessionId as string;

    if (!sessionId) {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(400).json({ error: 'sessionId query parameter is required' });
    }

    // Get Firebase admin instances
    const { firestore } = getFirebaseAdmin();

    // Get session from Firestore
    const sessionDoc = await firestore
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(404).json({ error: 'Session not found' });
    }

    const sessionData = sessionDoc.data();
    if (!sessionData) {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(404).json({ error: 'Session data not found' });
    }

    // Verify user owns this session
    verifyOwnership(authContext, sessionData.userId);

    // Get agent context from memory
    const agentContext = MemoryManager.getContext(sessionId);
    const contextSummary = MemoryManager.getContextSummary(sessionId);

    // Prepare response data
    const responseData = {
      session: {
        id: sessionId,
        ...sessionData,
        updatedAt: sessionData.updatedAt?.toDate?.() || sessionData.updatedAt,
        createdAt: sessionData.createdAt?.toDate?.() || sessionData.createdAt,
      },
      agentContext: agentContext ? {
        sessionId: agentContext.sessionId,
        userId: agentContext.userId,
        memoriesCount: Object.keys(agentContext.memories).length,
        agentStatusesCount: Object.keys(agentContext.agentStatuses).length,
        createdAt: agentContext.createdAt,
        lastAccessed: agentContext.lastAccessed,
      } : null,
      agentStatuses: agentContext?.agentStatuses || {},
      contextSummary: contextSummary,
      isActive: !!agentContext,
    };

    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    return res.status(200).json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Session status error:', error);

    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    if (error instanceof Error && 'statusCode' in error) {
      return res.status((error as any).statusCode || 400).json({
        error: error.message
      });
    }

    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get session status'
    });
  }
}
