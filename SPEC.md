# Podcast-to-YouTube-Description Generator
## Technical Specification v1.0

### 1. Product Vision

A single-page Angular 20 web application that transforms podcast audio files into rich YouTube descriptions through AI-powered multi-agent processing. Users upload audio, interact with an intelligent assistant via chat, and watch real-time edits in a split-pane interface similar to ChatGPT's editor mode.

**Core Features:**
- Audio transcription with word-level timestamps
- AI-generated YouTube descriptions with guest/host bios, topics, resources, SEO optimization
- Interactive chat-based editing with live preview
- Auto-mode for hands-free processing
- Firebase authentication with user-specific API key storage
- Automatic link detection and resource extraction from podcast content

### 2. Architecture Overview

**Frontend:** Angular 20 + Tailwind CSS + Firebase SDK
**Backend:** Firebase Functions + LangChain JS Multi-Agent System
**Database:** Firestore for user data, API keys, and session history
**Storage:** Firebase Storage for audio files and processed data

### 3. Multi-Agent Architecture (LangChain JS)

| Agent | Purpose | Tools/Chains |
|-------|---------|--------------|
| **Coordinator** | Orchestrates workflow, manages global context, decides autonomous vs user-prompted actions | LangChain MultiAgentExecutor, ConversationBufferMemory |
| **Transcriber** | Audio → text + word-level timestamps | Deepgram/Whisper API chain |
| **Topic Extractor** | Extracts themes, quotes, entities, SEO keywords from transcript | MapReduceDocuments + OpenAI GPT-4 |
| **Link Finder** | Automatically detects and validates URLs, products, books, tools mentioned in transcript | SerpAPI + URL validation chains |
| **Profile Finder** | Searches LinkedIn/Instagram/X for guest & host profiles | LangChain Tool + SerpAPI/custom crawler |
| **Reviewer** | Binary validation of profile matches, falls back to user confirmation | Simple LLM chain with confidence scoring |
| **Description Writer** | Assembles final YouTube description using all gathered data | Prompt-template chain with structured output |
| **Editor** | Processes user chat commands for real-time description modifications | Conversational chain with memory |

**Shared Memory:** All agents use LangChain ConversationBufferMemory for context persistence.

### 4. Core Data Contracts

```typescript
interface PodcastDescription {
  guestBio: string;
  hostBio: string;
  overview: string;
  keyTopics: string[];
  resources: ResourceLink[];
  timestamps: Timestamp[];
  extended: string;
  seo: SEOData;
  lastModified: Date;
  version: number;
}

interface ResourceLink {
  label: string;
  url: string;
  type: 'website' | 'book' | 'tool' | 'social' | 'product';
  confidence: number;
  extractedFrom: string; // transcript segment
}

interface Timestamp {
  time: string; // "MM:SS" format
  label: string;
  confidence: number;
}

interface SEOData {
  keywords: string[];
  hashtags: string[];
  suggestedTitle: string;
}

interface UserAPIKeys {
  openai?: string;
  deepgram?: string;
  serpapi?: string;
  anthropic?: string;
}

interface ProcessingSession {
  id: string;
  userId: string;
  audioFileUrl: string;
  transcript: string;
  description: PodcastDescription;
  chatHistory: ChatMessage[];
  status: 'uploading' | 'transcribing' | 'processing' | 'ready' | 'error';
  createdAt: Date;
  updatedAt: Date;
}
```

### 5. Directory Structure

```
pedma/
├── src/
│   ├── app/
│   │   ├── components/
│   │   │   ├── upload-zone/
│   │   │   ├── chat-panel/
│   │   │   ├── description-editor/
│   │   │   ├── progress-indicator/
│   │   │   └── auth/
│   │   ├── services/
│   │   │   ├── firebase.service.ts
│   │   │   ├── agent-coordinator.service.ts
│   │   │   ├── audio-processing.service.ts
│   │   │   └── description.service.ts
│   │   ├── models/
│   │   │   ├── podcast-description.model.ts
│   │   │   ├── user.model.ts
│   │   │   └── session.model.ts
│   │   ├── guards/
│   │   │   └── auth.guard.ts
│   │   └── app.component.ts
│   ├── environments/
│   └── assets/
├── functions/
│   ├── src/
│   │   ├── agents/
│   │   │   ├── coordinator.ts
│   │   │   ├── transcriber.ts
│   │   │   ├── topic-extractor.ts
│   │   │   ├── link-finder.ts
│   │   │   ├── profile-finder.ts
│   │   │   ├── reviewer.ts
│   │   │   ├── description-writer.ts
│   │   │   └── editor.ts
│   │   ├── utils/
│   │   │   ├── langchain-config.ts
│   │   │   └── memory-manager.ts
│   │   └── index.ts
│   ├── package.json
│   └── tsconfig.json
├── firebase.json
├── firestore.rules
├── storage.rules
├── package.json
├── angular.json
├── tailwind.config.js
└── README.md
```

### 6. User Interface Design

**Layout:** Split-pane interface with responsive breakpoints
- **Left Pane (40%):** Chat interface with auto-mode toggle
- **Right Pane (60%):** Live description editor with real-time updates
- **Top Bar:** User profile, dark mode toggle, export options

**Key Components:**

1. **Upload Zone** (`upload-zone/`)
   - Drag-and-drop audio file upload
   - Progress indicator during upload
   - File validation (mp3, wav, m4a, max 100MB)

2. **Chat Panel** (`chat-panel/`)
   - Message history with user/AI distinction
   - Auto-mode toggle switch
   - Input field with send button
   - Typing indicators during AI processing

3. **Description Editor** (`description-editor/`)
   - Live preview of YouTube description
   - Section-based editing (bio, overview, topics, etc.)
   - Highlight changes as they occur
   - Copy/export functionality

4. **Progress Indicator** (`progress-indicator/`)
   - Multi-stage progress bar
   - Current agent activity display
   - Estimated time remaining

### 7. User Flow

1. **Authentication**
   - Firebase Auth login (Google/Email)
   - API key setup on first login
   - Persistent key storage per user

2. **Upload & Processing**
   - Drag-and-drop audio file
   - Upload to Firebase Storage
   - Trigger Coordinator agent
   - Real-time progress updates

3. **AI Processing Pipeline**
   - Transcriber: Audio → text + timestamps
   - Topic Extractor: Themes + SEO keywords
   - Link Finder: Auto-detect mentioned resources
   - Profile Finder: Search for guest/host profiles
   - Reviewer: Validate profile matches
   - Description Writer: Assemble final description

4. **Interactive Editing**
   - Chat-based commands ("Make the intro punchier")
   - Real-time description updates
   - Auto-mode for hands-free completion
   - Manual section editing available

5. **Export & Save**
   - Copy formatted description
   - Save session to Firestore
   - Download as .txt or .md

### 8. Environment Variables

```bash
# Firebase Configuration
FIREBASE_API_KEY=
FIREBASE_AUTH_DOMAIN=
FIREBASE_PROJECT_ID=
FIREBASE_STORAGE_BUCKET=
FIREBASE_MESSAGING_SENDER_ID=
FIREBASE_APP_ID=

# AI Service APIs (stored per-user in Firestore)
OPENAI_API_KEY=user_specific
DEEPGRAM_API_KEY=user_specific
SERPAPI_KEY=user_specific
ANTHROPIC_API_KEY=user_specific

# Application Settings
ENVIRONMENT=development|production
MAX_FILE_SIZE_MB=100
SUPPORTED_FORMATS=mp3,wav,m4a,mp4
SESSION_TIMEOUT_HOURS=24
```

### 9. Firebase Configuration

**Firestore Collections:**
```
users/{userId}
  - email: string
  - apiKeys: UserAPIKeys
  - preferences: object
  - createdAt: timestamp

sessions/{sessionId}
  - userId: string
  - audioFileUrl: string
  - transcript: string
  - description: PodcastDescription
  - chatHistory: ChatMessage[]
  - status: string
  - createdAt: timestamp
  - updatedAt: timestamp

templates/{templateId}
  - name: string
  - structure: object
  - isPublic: boolean
  - createdBy: string
```

**Storage Structure:**
```
audio-files/{userId}/{sessionId}/original.{ext}
transcripts/{userId}/{sessionId}/transcript.json
descriptions/{userId}/{sessionId}/final.json
```

### 10. Testing Strategy

**Unit Tests:**
- Agent functionality (transcription, topic extraction, etc.)
- Service layer (Firebase, audio processing)
- Component logic (upload, chat, editor)

**Integration Tests:**
- Multi-agent workflow coordination
- Firebase operations (auth, storage, Firestore)
- Real-time updates between chat and editor

**E2E Tests:**
- Complete user journey (upload → process → edit → export)
- Auto-mode functionality
- Error handling and recovery

**Acceptance Criteria:**
- Audio files under 100MB process within 5 minutes
- Description accuracy >85% based on transcript content
- Real-time updates appear within 2 seconds
- Auto-detected links have >90% accuracy
- System handles concurrent users (100+)

### 11. Deployment Configuration

**Platform:** Vercel (Frontend) + Firebase (Backend)

**Build Process:**
```bash
# Install dependencies
npm install

# Build Angular app
ng build --configuration=production

# Deploy Firebase Functions
firebase deploy --only functions

# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy Storage rules
firebase deploy --only storage
```

**Vercel Configuration:**
```json
{
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist/pedma"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

**Environment Setup:**
- Development: Local Firebase emulators
- Staging: Firebase staging project
- Production: Firebase production project with CDN

### 12. Security Considerations

**API Key Management:**
- User API keys encrypted in Firestore
- Keys never exposed to client-side code
- Rotation mechanism for compromised keys

**File Upload Security:**
- File type validation on client and server
- Virus scanning for uploaded files
- Size limits and rate limiting

**Authentication:**
- Firebase Auth with secure token refresh
- Session timeout after inactivity
- Role-based access control

### 13. Performance Optimization

**Frontend:**
- Lazy loading for components
- Angular OnPush change detection
- Service worker for caching
- Image optimization for UI assets

**Backend:**
- Firebase Functions with appropriate memory allocation
- Streaming responses for long-running operations
- Caching for frequently accessed data
- Connection pooling for external APIs

### 14. Monitoring & Analytics

**Error Tracking:**
- Firebase Crashlytics integration
- Custom error logging for agent failures
- User feedback collection

**Performance Metrics:**
- Processing time per audio duration
- Agent success rates
- User engagement metrics
- API usage tracking

### 15. Stretch Goals

**Phase 2 Features:**
- **Speaker Diarization:** Identify multiple speakers in podcast
- **LinkedIn Auto-Share:** Direct posting to LinkedIn with generated content
- **Batch Processing:** Upload multiple episodes at once
- **Custom Templates:** User-defined description formats
- **Collaboration:** Share sessions with team members
- **Analytics Dashboard:** Track description performance metrics

**Advanced AI Features:**
- **Sentiment Analysis:** Tone detection for better descriptions
- **Topic Clustering:** Group related episodes automatically
- **Trend Analysis:** Suggest trending topics to include
- **Multi-language Support:** Transcription and description in multiple languages

**Integration Opportunities:**
- **YouTube API:** Direct upload to YouTube with generated descriptions
- **Podcast Platforms:** Integration with Spotify, Apple Podcasts
- **CRM Systems:** Export guest information to contact management
- **Social Media:** Auto-generate posts for Twitter, Instagram

### 16. Success Metrics

**Technical KPIs:**
- 99.9% uptime
- <5 minute processing time for 60-minute podcasts
- <2 second response time for chat interactions
- 95% user satisfaction score

**Business KPIs:**
- User retention rate >70% after 30 days
- Average session duration >15 minutes
- Description accuracy rating >4.5/5
- Monthly active users growth >20%

---

## Implementation Checklist

- [ ] Set up Firebase project with authentication
- [ ] Configure Firestore collections and security rules
- [ ] Implement Angular 20 application with Tailwind CSS
- [ ] Develop LangChain multi-agent system
- [ ] Create Firebase Functions for agent coordination
- [ ] Implement real-time chat and editor synchronization
- [ ] Add file upload and processing pipeline
- [ ] Integrate external APIs (Deepgram, OpenAI, SerpAPI)
- [ ] Implement user API key management
- [ ] Add comprehensive testing suite
- [ ] Configure deployment pipeline
- [ ] Set up monitoring and analytics
- [ ] Create user documentation and onboarding

**Estimated Timeline:** 8-12 weeks for MVP, additional 4-6 weeks for stretch goals

**Team Requirements:** 2-3 full-stack developers, 1 AI/ML specialist, 1 UI/UX designer
