import { NextApiRequest, NextApiResponse } from 'next';
import { MemoryManager } from './lib/utils/memory-manager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.status(200).end();
    return;
  }

  try {
    const origin = req.headers.get('origin');
    
    // Only allow GET requests
    if (req.method !== 'GET') {
      return createApiResponse(
        { success: false, error: 'Method not allowed' },
        405,
        origin
      );
    }

    // Get memory stats
    const memoryStats = MemoryManager.getStats();
    
    // Check environment variables
    const envCheck = {
      hasGroqKey: !!process.env.GROQ_API_KEY,
      hasFirebaseConfig: !!(
        process.env.FIREBASE_PROJECT_ID &&
        process.env.FIREBASE_CLIENT_EMAIL &&
        process.env.FIREBASE_PRIVATE_KEY
      ),
      hasDeepgramKey: !!process.env.DEEPGRAM_API_KEY,
      hasSerpApiKey: !!process.env.SERPAPI_KEY,
    };

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      memory: {
        activeSessions: memoryStats.totalSessions,
        memoryUsage: `${Math.round(memoryStats.totalMemorySize / 1024)} KB`,
        oldestSession: memoryStats.oldestSession,
        newestSession: memoryStats.newestSession,
      },
      services: {
        groq: envCheck.hasGroqKey ? 'configured' : 'missing',
        firebase: envCheck.hasFirebaseConfig ? 'configured' : 'missing',
        deepgram: envCheck.hasDeepgramKey ? 'configured' : 'missing',
        serpapi: envCheck.hasSerpApiKey ? 'configured' : 'missing',
      },
      uptime: process.uptime ? `${Math.round(process.uptime())}s` : 'unknown',
    };

    const origin = req.headers.origin as string;
    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    return res.status(200).json({
      success: true,
      data: healthData
    });
  } catch (error) {
    console.error('Health check failed:', error);

    const origin = req.headers.origin as string;
    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    return res.status(500).json({
      success: false,
      error: 'Health check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
