import { ApplicationConfig, provideBrowserGlobalErrorListeners, provideZonelessChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getAuth, provideAuth, connectAuthEmulator } from '@angular/fire/auth';
import { getFirestore, provideFirestore, connectFirestoreEmulator } from '@angular/fire/firestore';
import { getStorage, provideStorage, connectStorageEmulator } from '@angular/fire/storage';
import { getFunctions, provideFunctions, connectFunctionsEmulator } from '@angular/fire/functions';

import { routes } from './app.routes';
import { provideClientHydration, withEventReplay } from '@angular/platform-browser';
import { environment } from '../environments/environment';

export const appConfig: ApplicationConfig = {
  providers: [
    provideBrowserGlobalErrorListeners(),
    provideZonelessChangeDetection(),
    provideRouter(routes),
    provideClientHydration(withEventReplay()),
    provideFirebaseApp(() => initializeApp(environment.firebase)),
    provideAuth(() => {
      const auth = getAuth();
      // Emulators disabled for production use
      // if (!environment.production) {
      //   connectAuthEmulator(auth, 'http://127.0.0.1:9099', { disableWarnings: true });
      // }
      return auth;
    }),
    provideFirestore(() => {
      const firestore = getFirestore();
      // Emulators disabled for production use
      // if (!environment.production) {
      //   connectFirestoreEmulator(firestore, '127.0.0.1', 8082);
      // }
      return firestore;
    }),
    provideStorage(() => {
      const storage = getStorage();
      // Emulators disabled for production use
      // if (!environment.production) {
      //   connectStorageEmulator(storage, '127.0.0.1', 9199);
      // }
      return storage;
    }),
    provideFunctions(() => {
      const functions = getFunctions();
      // Emulators disabled for production use
      // if (!environment.production) {
      //   connectFunctionsEmulator(functions, '127.0.0.1', 5001);
      // }
      return functions;
    })
  ]
};
