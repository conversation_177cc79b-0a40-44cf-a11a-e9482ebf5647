import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { AppErrorHandlerService, AppError } from '../../services/error-handler.service';

@Component({
  selector: 'app-error-notifications',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- Error Notifications Container -->
    <div class="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
      <div *ngFor="let error of errors; trackBy: trackByErrorId"
           class="transform transition-all duration-300 ease-in-out"
           [class]="getErrorClasses(error)"
           [@slideIn]>
        
        <!-- Error Content -->
        <div class="flex items-start space-x-3 p-4">
          <!-- Icon -->
          <div class="flex-shrink-0 mt-0.5">
            <!-- Success Icon -->
            <svg *ngIf="error.type === 'success'" class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            
            <!-- Info Icon -->
            <svg *ngIf="error.type === 'info'" class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
            </svg>
            
            <!-- Warning Icon -->
            <svg *ngIf="error.type === 'warning'" class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            
            <!-- Error Icon -->
            <svg *ngIf="error.type === 'error'" class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
          </div>
          
          <!-- Content -->
          <div class="flex-1 min-w-0">
            <h4 class="text-sm font-medium" [class]="getTitleClasses(error)">
              {{ error.title }}
            </h4>
            <p class="text-sm mt-1" [class]="getMessageClasses(error)">
              {{ error.message }}
            </p>
            
            <!-- Details (expandable) -->
            <div *ngIf="error.details && showDetails[error.id]" 
                 class="mt-2 p-2 bg-gray-100 rounded text-xs font-mono text-gray-600 max-h-32 overflow-y-auto">
              {{ error.details }}
            </div>
            
            <!-- Actions -->
            <div class="mt-3 flex items-center space-x-3">
              <!-- Custom Action -->
              <button *ngIf="error.action" 
                      (click)="executeAction(error)"
                      class="text-xs font-medium underline hover:no-underline"
                      [class]="getActionClasses(error)">
                {{ error.action.label }}
              </button>
              
              <!-- Show Details Toggle -->
              <button *ngIf="error.details" 
                      (click)="toggleDetails(error.id)"
                      class="text-xs text-gray-500 hover:text-gray-700 underline hover:no-underline">
                {{ showDetails[error.id] ? 'Hide Details' : 'Show Details' }}
              </button>
              
              <!-- Timestamp -->
              <span class="text-xs text-gray-400">
                {{ getRelativeTime(error.timestamp) }}
              </span>
            </div>
          </div>
          
          <!-- Dismiss Button -->
          <div *ngIf="error.dismissible" class="flex-shrink-0">
            <button (click)="dismissError(error.id)"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Progress Bar for Auto-hide -->
        <div *ngIf="error.autoHide && error.duration" 
             class="h-1 bg-gray-200 overflow-hidden">
          <div class="h-full transition-all ease-linear"
               [class]="getProgressBarClasses(error)"
               [style.animation-duration.ms]="error.duration"
               style="animation-name: shrink; animation-timing-function: linear; animation-fill-mode: forwards;">
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    @keyframes shrink {
      from {
        width: 100%;
      }
      to {
        width: 0%;
      }
    }
    
    .error-notification {
      animation: slideIn 0.3s ease-out;
    }
  `],
  animations: []
})
export class ErrorNotificationsComponent implements OnInit, OnDestroy {
  private errorHandler = inject(AppErrorHandlerService);
  private subscription?: Subscription;
  
  errors: AppError[] = [];
  showDetails: Record<string, boolean> = {};

  ngOnInit() {
    this.subscription = this.errorHandler.errors$.subscribe(errors => {
      this.errors = errors;
    });
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  trackByErrorId(index: number, error: AppError): string {
    return error.id;
  }

  dismissError(id: string) {
    this.errorHandler.dismissError(id);
    delete this.showDetails[id];
  }

  executeAction(error: AppError) {
    if (error.action) {
      error.action.handler();
    }
  }

  toggleDetails(id: string) {
    this.showDetails[id] = !this.showDetails[id];
  }

  getErrorClasses(error: AppError): string {
    const baseClasses = 'rounded-lg shadow-lg border-l-4 bg-white error-notification';
    
    switch (error.type) {
      case 'success':
        return `${baseClasses} border-green-400`;
      case 'info':
        return `${baseClasses} border-blue-400`;
      case 'warning':
        return `${baseClasses} border-yellow-400`;
      case 'error':
      default:
        return `${baseClasses} border-red-400`;
    }
  }

  getTitleClasses(error: AppError): string {
    switch (error.type) {
      case 'success':
        return 'text-green-800';
      case 'info':
        return 'text-blue-800';
      case 'warning':
        return 'text-yellow-800';
      case 'error':
      default:
        return 'text-red-800';
    }
  }

  getMessageClasses(error: AppError): string {
    switch (error.type) {
      case 'success':
        return 'text-green-700';
      case 'info':
        return 'text-blue-700';
      case 'warning':
        return 'text-yellow-700';
      case 'error':
      default:
        return 'text-red-700';
    }
  }

  getActionClasses(error: AppError): string {
    switch (error.type) {
      case 'success':
        return 'text-green-600 hover:text-green-800';
      case 'info':
        return 'text-blue-600 hover:text-blue-800';
      case 'warning':
        return 'text-yellow-600 hover:text-yellow-800';
      case 'error':
      default:
        return 'text-red-600 hover:text-red-800';
    }
  }

  getProgressBarClasses(error: AppError): string {
    switch (error.type) {
      case 'success':
        return 'bg-green-400';
      case 'info':
        return 'bg-blue-400';
      case 'warning':
        return 'bg-yellow-400';
      case 'error':
      default:
        return 'bg-red-400';
    }
  }

  getRelativeTime(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (seconds < 60) {
      return 'just now';
    } else if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return timestamp.toLocaleDateString();
    }
  }
}
