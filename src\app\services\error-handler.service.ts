import { Injectable, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface AppError {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  details?: string;
  timestamp: Date;
  source?: string;
  action?: {
    label: string;
    handler: () => void;
  };
  dismissible: boolean;
  autoHide: boolean;
  duration?: number;
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  additionalData?: any;
}

@Injectable({
  providedIn: 'root'
})
export class AppErrorHandlerService implements ErrorHandler {
  private errorsSubject = new BehaviorSubject<AppError[]>([]);
  public errors$ = this.errorsSubject.asObservable();

  private readonly MAX_ERRORS = 10;
  private readonly DEFAULT_DURATION = 5000; // 5 seconds

  handleError(error: any, context?: ErrorContext): void {
    console.error('Global error handler:', error, context);

    // Create structured error
    const appError = this.createAppError(error, context);
    
    // Add to error list
    this.addError(appError);

    // Log to external service if configured
    this.logToExternalService(error, context);
  }

  /**
   * Show a user-friendly error message
   */
  showError(
    title: string, 
    message: string, 
    options?: Partial<AppError>
  ): string {
    const error: AppError = {
      id: this.generateId(),
      type: 'error',
      title,
      message,
      timestamp: new Date(),
      dismissible: true,
      autoHide: false,
      ...options
    };

    this.addError(error);
    return error.id;
  }

  /**
   * Show a warning message
   */
  showWarning(
    title: string, 
    message: string, 
    options?: Partial<AppError>
  ): string {
    const warning: AppError = {
      id: this.generateId(),
      type: 'warning',
      title,
      message,
      timestamp: new Date(),
      dismissible: true,
      autoHide: true,
      duration: this.DEFAULT_DURATION,
      ...options
    };

    this.addError(warning);
    return warning.id;
  }

  /**
   * Show an info message
   */
  showInfo(
    title: string, 
    message: string, 
    options?: Partial<AppError>
  ): string {
    const info: AppError = {
      id: this.generateId(),
      type: 'info',
      title,
      message,
      timestamp: new Date(),
      dismissible: true,
      autoHide: true,
      duration: this.DEFAULT_DURATION,
      ...options
    };

    this.addError(info);
    return info.id;
  }

  /**
   * Show a success message
   */
  showSuccess(
    title: string, 
    message: string, 
    options?: Partial<AppError>
  ): string {
    const success: AppError = {
      id: this.generateId(),
      type: 'success',
      title,
      message,
      timestamp: new Date(),
      dismissible: true,
      autoHide: true,
      duration: this.DEFAULT_DURATION,
      ...options
    };

    this.addError(success);
    return success.id;
  }

  /**
   * Dismiss an error by ID
   */
  dismissError(id: string): void {
    const currentErrors = this.errorsSubject.value;
    const updatedErrors = currentErrors.filter(error => error.id !== id);
    this.errorsSubject.next(updatedErrors);
  }

  /**
   * Clear all errors
   */
  clearAllErrors(): void {
    this.errorsSubject.next([]);
  }

  /**
   * Get current errors
   */
  getCurrentErrors(): AppError[] {
    return this.errorsSubject.value;
  }

  /**
   * Handle specific error types
   */
  handleUploadError(error: any, fileName?: string): void {
    let message = 'Failed to upload file';
    if (fileName) {
      message += `: ${fileName}`;
    }

    if (error?.message?.includes('size')) {
      message = 'File size exceeds the maximum limit of 100MB';
    } else if (error?.message?.includes('type')) {
      message = 'File type not supported. Please use MP3, WAV, M4A, or MP4 files';
    } else if (error?.message?.includes('network')) {
      message = 'Network error. Please check your connection and try again';
    }

    this.showError('Upload Failed', message, {
      source: 'upload',
      action: {
        label: 'Try Again',
        handler: () => {
          // Emit retry event or handle retry logic
          console.log('Retry upload requested');
        }
      }
    });
  }

  handleAuthError(error: any): void {
    let message = 'Authentication failed';
    
    if (error?.code === 'auth/user-not-found') {
      message = 'No account found with this email address';
    } else if (error?.code === 'auth/wrong-password') {
      message = 'Incorrect password';
    } else if (error?.code === 'auth/too-many-requests') {
      message = 'Too many failed attempts. Please try again later';
    } else if (error?.code === 'auth/network-request-failed') {
      message = 'Network error. Please check your connection';
    }

    this.showError('Authentication Error', message, {
      source: 'auth'
    });
  }

  handleApiError(error: any, apiName: string): void {
    let message = `${apiName} API error`;
    
    if (error?.status === 401) {
      message = `Invalid ${apiName} API key. Please check your configuration`;
    } else if (error?.status === 429) {
      message = `${apiName} API rate limit exceeded. Please try again later`;
    } else if (error?.status === 500) {
      message = `${apiName} service is temporarily unavailable`;
    } else if (error?.message?.includes('network')) {
      message = 'Network error. Please check your connection';
    }

    this.showError('API Error', message, {
      source: 'api',
      details: `Service: ${apiName}, Status: ${error?.status || 'Unknown'}`
    });
  }

  handleProcessingError(error: any, stage: string): void {
    let message = `Processing failed at ${stage} stage`;
    
    if (stage === 'transcription') {
      message = 'Audio transcription failed. Please try with a different audio file';
    } else if (stage === 'description') {
      message = 'Description generation failed. Please try again';
    } else if (stage === 'analysis') {
      message = 'Content analysis failed. Please try again';
    }

    this.showError('Processing Error', message, {
      source: 'processing',
      action: {
        label: 'Retry',
        handler: () => {
          console.log(`Retry ${stage} requested`);
        }
      }
    });
  }

  private createAppError(error: any, context?: ErrorContext): AppError {
    let title = 'Unexpected Error';
    let message = 'An unexpected error occurred';
    let source = context?.component || 'unknown';

    // Extract meaningful information from different error types
    if (error instanceof Error) {
      title = error.name || 'Error';
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // Enhance message based on context
    if (context?.action) {
      message = `Failed to ${context.action}: ${message}`;
    }

    return {
      id: this.generateId(),
      type: 'error',
      title,
      message,
      details: this.getErrorDetails(error),
      timestamp: new Date(),
      source,
      dismissible: true,
      autoHide: false
    };
  }

  private addError(error: AppError): void {
    const currentErrors = this.errorsSubject.value;
    const updatedErrors = [error, ...currentErrors].slice(0, this.MAX_ERRORS);
    this.errorsSubject.next(updatedErrors);

    // Auto-hide if configured
    if (error.autoHide && error.duration) {
      setTimeout(() => {
        this.dismissError(error.id);
      }, error.duration);
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private getErrorDetails(error: any): string {
    if (error?.stack) {
      return error.stack;
    } else if (error?.details) {
      return error.details;
    } else if (typeof error === 'object') {
      return JSON.stringify(error, null, 2);
    }
    return '';
  }

  private logToExternalService(error: any, context?: ErrorContext): void {
    // TODO: Implement external logging service (e.g., Sentry, LogRocket)
    // This would send errors to a monitoring service for analysis
    
    const errorData = {
      error: {
        message: error?.message || error,
        stack: error?.stack,
        name: error?.name
      },
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // For now, just log to console in development
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      console.group('🔴 Error Report');
      console.error('Error:', error);
      console.log('Context:', context);
      console.log('Full Report:', errorData);
      console.groupEnd();
    }
  }
}
