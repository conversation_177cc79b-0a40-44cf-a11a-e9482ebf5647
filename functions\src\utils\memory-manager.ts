import { AgentContext, AgentStatus } from '../types';

export class MemoryManager {
  private static contexts: Map<string, AgentContext> = new Map();

  static getContext(sessionId: string): AgentContext | null {
    return this.contexts.get(sessionId) || null;
  }

  static setContext(sessionId: string, context: AgentContext): void {
    this.contexts.set(sessionId, context);
  }

  static updateContext(sessionId: string, updates: Partial<AgentContext>): AgentContext {
    const existing = this.getContext(sessionId);
    if (!existing) {
      throw new Error(`No context found for session ${sessionId}`);
    }

    const updated = {
      ...existing,
      ...updates,
      memory: {
        ...existing.memory,
        ...updates.memory
      },
      agentStatuses: {
        ...existing.agentStatuses,
        ...updates.agentStatuses
      }
    };

    this.setContext(sessionId, updated);
    return updated;
  }

  static updateAgentStatus(sessionId: string, agentName: string, status: AgentStatus): void {
    const context = this.getContext(sessionId);
    if (context) {
      context.agentStatuses[agentName] = status;
      this.setContext(sessionId, context);
    }
  }

  static getAgentStatus(sessionId: string, agentName: string): AgentStatus | null {
    const context = this.getContext(sessionId);
    return context?.agentStatuses[agentName] || null;
  }

  static addMemory(sessionId: string, key: string, value: any): void {
    const context = this.getContext(sessionId);
    if (context) {
      context.memory[key] = value;
      this.setContext(sessionId, context);
    }
  }

  static getMemory(sessionId: string, key: string): any {
    const context = this.getContext(sessionId);
    return context?.memory[key];
  }

  static clearContext(sessionId: string): void {
    this.contexts.delete(sessionId);
  }

  static createInitialContext(sessionId: string, userId: string, transcript: string = ''): AgentContext {
    const context: AgentContext = {
      sessionId,
      userId,
      transcript,
      currentDescription: {},
      agentStatuses: {},
      memory: {}
    };

    this.setContext(sessionId, context);
    return context;
  }

  static getAllActiveSessions(): string[] {
    return Array.from(this.contexts.keys());
  }

  static getContextSummary(sessionId: string): any {
    const context = this.getContext(sessionId);
    if (!context) return null;

    return {
      sessionId: context.sessionId,
      userId: context.userId,
      hasTranscript: !!context.transcript,
      transcriptLength: context.transcript?.length || 0,
      descriptionProgress: Object.keys(context.currentDescription).length,
      activeAgents: Object.keys(context.agentStatuses).filter(
        agent => context.agentStatuses[agent].status === 'processing'
      ),
      completedAgents: Object.keys(context.agentStatuses).filter(
        agent => context.agentStatuses[agent].status === 'complete'
      ),
      memoryKeys: Object.keys(context.memory)
    };
  }

  static exportContext(sessionId: string): AgentContext | null {
    return this.getContext(sessionId);
  }

  static importContext(sessionId: string, context: AgentContext): void {
    this.setContext(sessionId, context);
  }

  // Cleanup old contexts (call periodically)
  static cleanup(maxAgeHours: number = 24): number {
    const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
    let cleaned = 0;

    for (const [sessionId, context] of this.contexts.entries()) {
      // Check if any agent has been active recently
      const lastActivity = Math.max(
        ...Object.values(context.agentStatuses)
          .map(status => status.endTime?.getTime() || status.startTime?.getTime() || 0)
      );

      if (lastActivity < cutoffTime) {
        this.contexts.delete(sessionId);
        cleaned++;
      }
    }

    return cleaned;
  }
}
