import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AgentStatus } from '../../models/podcast-description.model';

interface ProcessingStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'processing' | 'complete' | 'error';
  progress: number;
  message?: string;
  estimatedTime?: number;
}

@Component({
  selector: 'app-progress-indicator',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <!-- Header -->
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Processing Progress</h3>
        <div class="text-sm text-gray-500">
          {{ completedSteps }}/{{ totalSteps }} steps completed
        </div>
      </div>
      
      <!-- Overall Progress Bar -->
      <div class="mb-6">
        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
          <span>Overall Progress</span>
          <span>{{ overallProgress | number:'1.0-0' }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
          <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
               [style.width.%]="overallProgress">
          </div>
        </div>
        <div *ngIf="estimatedTimeRemaining > 0" class="text-xs text-gray-500 mt-1 text-center">
          Estimated time remaining: {{ formatTime(estimatedTimeRemaining) }}
        </div>
      </div>
      
      <!-- Processing Steps -->
      <div class="space-y-3">
        <div *ngFor="let step of processingSteps; trackBy: trackByStepId" 
             class="flex items-start space-x-3 p-3 rounded-lg transition-all duration-300"
             [ngClass]="{
               'bg-blue-50 border border-blue-200': step.status === 'processing',
               'bg-green-50 border border-green-200': step.status === 'complete',
               'bg-red-50 border border-red-200': step.status === 'error',
               'bg-gray-50 border border-gray-200': step.status === 'pending'
             }">
          
          <!-- Step Icon -->
          <div class="flex-shrink-0 mt-0.5">
            <!-- Pending -->
            <div *ngIf="step.status === 'pending'" 
                 class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
              <span class="text-xs font-medium text-gray-600">{{ getStepNumber(step.id) }}</span>
            </div>
            
            <!-- Processing -->
            <div *ngIf="step.status === 'processing'" 
                 class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            
            <!-- Complete -->
            <div *ngIf="step.status === 'complete'" 
                 class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
            </div>
            
            <!-- Error -->
            <div *ngIf="step.status === 'error'" 
                 class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          
          <!-- Step Content -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between">
              <h4 class="text-sm font-medium"
                  [ngClass]="{
                    'text-blue-900': step.status === 'processing',
                    'text-green-900': step.status === 'complete',
                    'text-red-900': step.status === 'error',
                    'text-gray-700': step.status === 'pending'
                  }">
                {{ step.name }}
              </h4>
              <span *ngIf="step.status === 'processing' && step.progress > 0" 
                    class="text-xs font-medium text-blue-600">
                {{ step.progress | number:'1.0-0' }}%
              </span>
            </div>
            
            <p class="text-xs mt-1"
               [ngClass]="{
                 'text-blue-700': step.status === 'processing',
                 'text-green-700': step.status === 'complete',
                 'text-red-700': step.status === 'error',
                 'text-gray-500': step.status === 'pending'
               }">
              {{ step.message || step.description }}
            </p>
            
            <!-- Step Progress Bar -->
            <div *ngIf="step.status === 'processing' && step.progress > 0" 
                 class="mt-2">
              <div class="w-full bg-blue-200 rounded-full h-1.5">
                <div class="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                     [style.width.%]="step.progress">
                </div>
              </div>
            </div>
            
            <!-- Estimated Time for Current Step -->
            <div *ngIf="step.status === 'processing' && step.estimatedTime" 
                 class="text-xs text-blue-600 mt-1">
              ~{{ formatTime(step.estimatedTime) }} remaining
            </div>
          </div>
        </div>
      </div>
      
      <!-- Error Actions -->
      <div *ngIf="hasErrors" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-start space-x-2">
          <svg class="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
          <div class="flex-1">
            <h4 class="text-sm font-medium text-red-900">Processing Error</h4>
            <p class="text-sm text-red-700 mt-1">
              Some steps failed to complete. You can retry the failed steps or start over.
            </p>
            <div class="mt-3 flex space-x-2">
              <button (click)="retryFailed()" 
                      class="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 transition-colors">
                Retry Failed Steps
              </button>
              <button (click)="startOver()" 
                      class="text-xs bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 transition-colors">
                Start Over
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Success Message -->
      <div *ngIf="isComplete && !hasErrors" 
           class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex items-center space-x-2">
          <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
          <div>
            <h4 class="text-sm font-medium text-green-900">Processing Complete!</h4>
            <p class="text-sm text-green-700">
              Your YouTube description has been generated successfully.
            </p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .animate-fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
  `]
})
export class ProgressIndicatorComponent implements OnChanges {
  @Input() agentStatuses: Record<string, AgentStatus> = {};
  @Input() overallProgress = 0;
  @Input() estimatedTimeRemaining = 0;
  
  processingSteps: ProcessingStep[] = [
    {
      id: 'transcriber',
      name: 'Audio Transcription',
      description: 'Converting audio to text with timestamps',
      status: 'pending',
      progress: 0
    },
    {
      id: 'topicExtractor',
      name: 'Topic Analysis',
      description: 'Extracting key themes and topics',
      status: 'pending',
      progress: 0
    },
    {
      id: 'linkFinder',
      name: 'Resource Detection',
      description: 'Finding mentioned links and resources',
      status: 'pending',
      progress: 0
    },
    {
      id: 'profileFinder',
      name: 'Profile Search',
      description: 'Searching for guest and host profiles',
      status: 'pending',
      progress: 0
    },
    {
      id: 'descriptionWriter',
      name: 'Description Generation',
      description: 'Creating the YouTube description',
      status: 'pending',
      progress: 0
    }
  ];
  
  get completedSteps(): number {
    return this.processingSteps.filter(step => step.status === 'complete').length;
  }
  
  get totalSteps(): number {
    return this.processingSteps.length;
  }
  
  get hasErrors(): boolean {
    return this.processingSteps.some(step => step.status === 'error');
  }
  
  get isComplete(): boolean {
    return this.completedSteps === this.totalSteps;
  }
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes['agentStatuses']) {
      this.updateStepsFromAgentStatuses();
    }
  }
  
  private updateStepsFromAgentStatuses() {
    this.processingSteps.forEach(step => {
      const agentStatus = this.agentStatuses[step.id];
      if (agentStatus) {
        step.status = this.mapAgentStatusToStepStatus(agentStatus.status);
        step.progress = agentStatus.progress || 0;
        step.message = agentStatus.message;
        
        // Calculate estimated time for current step
        if (step.status === 'processing' && agentStatus.startTime) {
          const elapsed = Date.now() - agentStatus.startTime.getTime();
          const progressRate = step.progress / elapsed;
          if (progressRate > 0) {
            step.estimatedTime = Math.round((100 - step.progress) / progressRate);
          }
        }
      }
    });
  }
  
  private mapAgentStatusToStepStatus(agentStatus: string): 'pending' | 'processing' | 'complete' | 'error' {
    switch (agentStatus) {
      case 'idle':
        return 'pending';
      case 'processing':
        return 'processing';
      case 'complete':
        return 'complete';
      case 'error':
        return 'error';
      default:
        return 'pending';
    }
  }
  
  getStepNumber(stepId: string): number {
    return this.processingSteps.findIndex(step => step.id === stepId) + 1;
  }
  
  trackByStepId(index: number, step: ProcessingStep): string {
    return step.id;
  }
  
  formatTime(milliseconds: number): string {
    const seconds = Math.ceil(milliseconds / 1000);
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
  }
  
  retryFailed() {
    // Emit event to parent component to retry failed steps
    console.log('Retrying failed steps...');
  }
  
  startOver() {
    // Emit event to parent component to start over
    console.log('Starting over...');
  }
}
