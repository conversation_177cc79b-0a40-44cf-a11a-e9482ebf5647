import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChatMessage } from '../../models/podcast-description.model';

@Component({
  selector: 'app-chat-panel',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="flex flex-col h-full">
      <!-- Chat Header -->
      <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">AI Assistant</h2>
        <p class="text-sm text-gray-600">Chat with AI to refine your description</p>
        
        <!-- Processing Progress -->
        <div *ngIf="processingProgress > 0 && processingProgress < 100" class="mt-3">
          <div class="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Processing...</span>
            <span>{{ processingProgress | number:'1.0-0' }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                 [style.width.%]="processingProgress">
            </div>
          </div>
          <div *ngIf="estimatedTimeRemaining > 0" class="text-xs text-gray-500 mt-1">
            Estimated time remaining: {{ estimatedTimeRemaining }}s
          </div>
        </div>
        
        <!-- Auto Mode Toggle -->
        <div class="mt-3 flex items-center space-x-3">
          <label class="flex items-center space-x-2 cursor-pointer">
            <input type="checkbox" 
                   [checked]="autoMode" 
                   (change)="onAutoModeChange($event)"
                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
            <span class="text-sm font-medium text-gray-700">Auto Mode</span>
          </label>
          <div class="text-xs text-gray-500">
            Automatically complete processing without prompts
          </div>
        </div>
      </div>
      
      <!-- Messages Container -->
      <div class="flex-1 overflow-y-auto p-4 space-y-4" #messagesContainer>
        <div *ngFor="let message of messages; trackBy: trackByMessageId" 
             [ngClass]="{
               'flex justify-end': message.role === 'user',
               'flex justify-start': message.role === 'assistant'
             }"
             class="animate-fade-in">
          <div [ngClass]="{
                 'bg-blue-600 text-white': message.role === 'user',
                 'bg-gray-100 text-gray-900': message.role === 'assistant' && message.type !== 'error' && message.type !== 'warning',
                 'bg-red-100 text-red-900 border border-red-200': message.type === 'error',
                 'bg-yellow-100 text-yellow-900 border border-yellow-200': message.type === 'warning',
                 'bg-blue-50 text-blue-900 border border-blue-200': message.type === 'system'
               }"
               class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow-sm">
            
            <!-- Message Icon -->
            <div *ngIf="message.type && message.type !== 'text'" class="flex items-start space-x-2 mb-1">
              <!-- Error Icon -->
              <svg *ngIf="message.type === 'error'" class="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
              </svg>
              
              <!-- Warning Icon -->
              <svg *ngIf="message.type === 'warning'" class="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
              
              <!-- System Icon -->
              <svg *ngIf="message.type === 'system'" class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
              </svg>
              
              <span class="text-xs font-medium capitalize">{{ message.type }}</span>
            </div>
            
            <!-- Message Content -->
            <div class="message-content">
              <p class="text-sm whitespace-pre-wrap">{{ message.content }}</p>
              <p class="text-xs opacity-75 mt-1">{{ message.timestamp | date:'short' }}</p>
            </div>
          </div>
        </div>
        
        <!-- Typing Indicator -->
        <div *ngIf="isTyping" class="flex justify-start animate-fade-in">
          <div class="bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow-sm">
            <div class="flex items-center space-x-2">
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
              <span class="text-xs text-gray-500">AI is thinking...</span>
            </div>
          </div>
        </div>
        
        <!-- Empty State -->
        <div *ngIf="messages.length === 0" class="flex flex-col items-center justify-center h-full text-center py-8">
          <svg class="w-12 h-12 text-gray-300 mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
          </svg>
          <h3 class="text-sm font-medium text-gray-900 mb-1">No messages yet</h3>
          <p class="text-xs text-gray-500">Start a conversation with the AI assistant</p>
        </div>
      </div>
      
      <!-- Input Area -->
      <div class="p-4 border-t border-gray-200 bg-gray-50">
        <!-- File Upload Area (when enabled) -->
        <div *ngIf="showFileInput" class="mb-4 p-4 border-2 border-dashed border-gray-300 rounded-lg">
          <div class="text-center">
            <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
            </svg>
            <p class="text-sm text-gray-600 mb-2">Upload transcript file or paste content</p>
            <div class="flex space-x-2 justify-center">
              <button (click)="fileInput.click()" class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                Choose File
              </button>
              <button (click)="showTextArea = true; showFileInput = false" class="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700">
                Paste Text
              </button>
              <button (click)="showFileInput = false" class="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                Cancel
              </button>
            </div>
            <input #fileInput
                   type="file"
                   accept=".txt,.srt,.vtt,text/plain"
                   (change)="onFileSelected($event)"
                   class="hidden">
          </div>
        </div>

        <!-- Large Text Area (when enabled) -->
        <div *ngIf="showTextArea" class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <label class="text-sm font-medium text-gray-700">Paste your transcript content:</label>
            <button (click)="showTextArea = false" class="text-sm text-gray-500 hover:text-gray-700">✕</button>
          </div>
          <textarea [(ngModel)]="transcriptContent"
                    placeholder="Paste your transcript content here..."
                    class="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                    [disabled]="isTyping || disabled"></textarea>
          <div class="flex justify-between items-center mt-2">
            <span class="text-xs text-gray-500">{{ transcriptContent.length }} characters</span>
            <div class="space-x-2">
              <button (click)="clearTranscript()" class="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                Clear
              </button>
              <button (click)="sendTranscript()" [disabled]="!transcriptContent.trim()" class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50">
                Send Transcript
              </button>
            </div>
          </div>
        </div>

        <!-- Regular Message Input -->
        <div class="flex space-x-2">
          <div class="flex-1 relative">
            <input type="text"
                   [(ngModel)]="currentMessage"
                   (keydown)="onKeyDown($event)"
                   (input)="onInputChange()"
                   placeholder="Type your message..."
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                   [disabled]="isTyping || disabled"
                   #messageInput>

            <!-- Character Count -->
            <div *ngIf="currentMessage.length > 0"
                 class="absolute right-2 top-2 text-xs text-gray-400">
              {{ currentMessage.length }}/500
            </div>
          </div>

          <!-- File Upload Button -->
          <button (click)="toggleFileInput()"
                  [disabled]="isTyping || disabled"
                  class="px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="Upload file or paste transcript">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
          </button>

          <!-- Send Button -->
          <button (click)="sendMessage()"
                  [disabled]="!canSendMessage()"
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            <svg *ngIf="!isTyping" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
            </svg>
            <svg *ngIf="isTyping" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </button>
        </div>
        
        <!-- Quick Actions -->
        <div *ngIf="showQuickActions" class="mt-2 flex flex-wrap gap-2">
          <button *ngFor="let action of quickActions" 
                  (click)="sendQuickAction(action)"
                  class="px-3 py-1 text-xs bg-white border border-gray-200 rounded-full hover:bg-gray-50 transition-colors duration-200">
            {{ action }}
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .animate-fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .message-content {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 6px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `]
})
export class ChatPanelComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  
  @Input() messages: ChatMessage[] = [];
  @Input() isTyping = false;
  @Input() autoMode = false;
  @Input() disabled = false;
  @Input() processingProgress = 0;
  @Input() estimatedTimeRemaining = 0;
  @Input() showQuickActions = true;
  
  @Output() messageSent = new EventEmitter<string>();
  @Output() autoModeChanged = new EventEmitter<boolean>();
  @Output() quickActionSelected = new EventEmitter<string>();
  @Output() fileUploaded = new EventEmitter<File>();
  @Output() transcriptPasted = new EventEmitter<string>();

  currentMessage = '';
  transcriptContent = '';
  showFileInput = false;
  showTextArea = false;
  private shouldScrollToBottom = false;
  
  quickActions = [
    'Make it more engaging',
    'Add more keywords',
    'Shorten the description',
    'Add timestamps',
    'Include call-to-action'
  ];
  
  ngOnInit() {
    this.shouldScrollToBottom = true;
  }
  
  ngOnDestroy() {
    // Cleanup if needed
  }
  
  ngAfterViewChecked() {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }
  
  onAutoModeChange(event: any) {
    this.autoModeChanged.emit(event.target.checked);
  }
  
  onKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }
  
  onInputChange() {
    // Limit message length
    if (this.currentMessage.length > 500) {
      this.currentMessage = this.currentMessage.substring(0, 500);
    }
  }
  
  sendMessage() {
    if (!this.canSendMessage()) return;
    
    const message = this.currentMessage.trim();
    this.messageSent.emit(message);
    this.currentMessage = '';
    this.shouldScrollToBottom = true;
    
    // Focus back to input
    setTimeout(() => {
      if (this.messageInput) {
        this.messageInput.nativeElement.focus();
      }
    }, 100);
  }
  
  sendQuickAction(action: string) {
    this.quickActionSelected.emit(action);
    this.shouldScrollToBottom = true;
  }
  
  canSendMessage(): boolean {
    return !this.isTyping && 
           !this.disabled && 
           this.currentMessage.trim().length > 0 && 
           this.currentMessage.length <= 500;
  }
  
  trackByMessageId(index: number, message: ChatMessage): string {
    return message.id;
  }

  // File input methods
  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.fileUploaded.emit(file);
      this.showFileInput = false;
      // Reset file input
      event.target.value = '';
    }
  }

  sendTranscript() {
    if (this.transcriptContent.trim()) {
      this.transcriptPasted.emit(this.transcriptContent.trim());
      this.clearTranscript();
      this.showTextArea = false;
    }
  }

  clearTranscript() {
    this.transcriptContent = '';
  }

  toggleFileInput() {
    this.showFileInput = !this.showFileInput;
    this.showTextArea = false;
  }

  private scrollToBottom(): void {
    try {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    } catch (err) {
      console.warn('Could not scroll to bottom:', err);
    }
  }
}
