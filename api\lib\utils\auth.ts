// lib/utils/auth.ts  – Node runtime helpers
import type { NextApiRequest, NextApiResponse } from 'next';
import * as admin from 'firebase-admin';
import { AuthenticationError, AuthorizationError } from '../types';

// ----- Firebase Admin initialisation -----
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId:  process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      // IMPORTANT: Keep the key inside quotes in Vercel/Firebase env vars
      privateKey:  process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
    databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com`,
    storageBucket: `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
  });
}

/* -------------------------------------------------------------------------- */
/*  Auth helpers                                                              */
/* -------------------------------------------------------------------------- */

export interface AuthContext {
  uid: string;
  email?: string;
  emailVerified?: boolean;
  customClaims?: Record<string, unknown>;
}

/** Verify Firebase ID token taken from the Node request */
export async function verifyAuth(req: NextApiRequest): Promise<AuthContext> {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid Authorization header');
    }

    const idToken = authHeader.slice(7);           // after "Bearer "
    const decoded = await admin.auth().verifyIdToken(idToken);

    return {
      uid: decoded.uid,
      email: decoded.email,
      emailVerified: decoded.email_verified,
      customClaims: decoded,
    };
  } catch (err: any) {
    console.error('Auth verification failed:', err);

    if (err instanceof AuthenticationError) throw err;

    if (err?.code === 'auth/id-token-expired')  throw new AuthenticationError('Token expired');
    if (err?.code === 'auth/id-token-revoked')  throw new AuthenticationError('Token revoked');
    if (err?.code === 'auth/invalid-id-token')  throw new AuthenticationError('Invalid token');

    throw new AuthenticationError('Authentication failed');
  }
}

/** Authorisation helper */
export function verifyOwnership(auth: AuthContext, resourceUserId: string) {
  if (auth.uid !== resourceUserId) {
    throw new AuthorizationError('User can only access their own resources');
  }
}

/* -------------------------------------------------------------------------- */
/*  Firebase handles                                                          */
/* -------------------------------------------------------------------------- */

export function getFirebaseAdmin() {
  return {
    auth:      admin.auth(),
    firestore: admin.firestore(),
    storage:   admin.storage(),
  };
}

/* -------------------------------------------------------------------------- */
/*  CORS utilities                                                            */
/* -------------------------------------------------------------------------- */

export function applyCorsHeaders(res: NextApiResponse, origin = '*') {
  res.setHeader('Access-Control-Allow-Origin', origin);
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Max-Age', '86400');
}

/** Generic CORS handler for Node API routes */
export function handleCors(req: NextApiRequest, res: NextApiResponse): boolean {
  const origin = req.headers.origin ?? '*';
  applyCorsHeaders(res, origin);

  // Abort further handling on OPTIONS
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return true;
  }
  return false;
}

/* -------------------------------------------------------------------------- */
/*  API helpers                                                               */
/* -------------------------------------------------------------------------- */

export function sendJson(
  res: NextApiResponse,
  payload: unknown,
  status = 200,
) {
  res.status(status).json(payload);
}

export function sendError(
  res: NextApiResponse,
  error: Error | string,
  status = 500,
) {
  const message = typeof error === 'string' ? error : error.message;
  const code = typeof error === 'object' && 'code' in error ? (error as any).code : undefined;

  sendJson(res, { success: false, error: message, code }, status);
}

/* -------------------------------------------------------------------------- */
/*  Request-body validator (JSON only)                                        */
/* -------------------------------------------------------------------------- */

export async function validateRequestBody<T>(
  req: NextApiRequest,
  required: string[],
): Promise<T> {
  if (req.headers['content-type'] !== 'application/json') {
    throw new Error('Expected application/json content type');
  }

  const body = req.body ?? {};
  for (const field of required) {
    if (!(field in body) || body[field] == null) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  return body as T;
}
