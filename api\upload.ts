// pages/api/upload.ts  – Node runtime, multipart-safe
import type { NextApiRequest, NextApiResponse } from 'next';
import { IncomingForm, File as FormidableFile } from 'formidable';
import fs from 'fs/promises';
import { randomUUID } from 'crypto';

import {
  handleCors,
  createErrorResponse,
  verifyAuth,
  verifyOwnership,
  getFirebaseAdmin,
} from '@/lib/utils/auth';
import { ValidationError } from '@/lib/types';

/** Tell Next.js **not** to parse the body – we’ll handle multipart ourselves. */
export const config = { api: { bodyParser: false } };

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  /* ---------- 1. CORS (OPTIONS pre-flight + normal requests) ---------- */
  if (handleCors(req, res)) return;           // OPTIONS short-circuit

  const origin = req.headers.origin ?? '*';

  try {
    /* ---------- 2. Only allow POST ---------- */
    if (req.method !== 'POST') {
      return createErrorResponse(res, 'Method not allowed', 405, origin);
    }

    /* ---------- 3. Auth ---------- */
    const authContext = await verifyAuth(req);          // pulls Bearer token

    /* ---------- 4. Parse multipart/form-data ---------- */
    const { fields, files } = await parseMultipart(req);
    const upload = files.file as FormidableFile | undefined;
    const sessionId = fields.sessionId as string | undefined;
    const userId = fields.userId as string | undefined;
    const fileType = (fields.fileType as string) || 'audio';

    if (!upload || !sessionId || !userId) {
      throw new ValidationError('Missing required fields: file, sessionId, userId');
    }

    /* ---------- 5. Ownership ---------- */
    verifyOwnership(authContext, userId);

    /* ---------- 6. Build Firebase-Storage path ---------- */
    const timestamp = Date.now();
    const ext = upload.originalFilename?.split('.').pop() ?? 'bin';
    const fileName = `${sessionId}_${timestamp}_${randomUUID()}.${ext}`;
    const filePath = `${fileType}-files/${userId}/${sessionId}/${fileName}`;

    /* ---------- 7. Read the temp file & upload ---------- */
    const buffer = await fs.readFile(upload.filepath);
    const { storage } = getFirebaseAdmin();
    const bucket = storage.bucket();
    const fileRef = bucket.file(filePath);

    await fileRef.save(buffer, {
      resumable: false,
      metadata: {
        contentType: upload.mimetype || 'application/octet-stream',
        metadata: {
          sessionId,
          userId,
          originalName: upload.originalFilename,
          uploadedAt: new Date().toISOString(),
        },
      },
    });

    // Optional: make public
    await fileRef.makePublic();
    const downloadURL = `https://storage.googleapis.com/${bucket.name}/${filePath}`;

    /* ---------- 8. Success ---------- */
    return res.status(200).json({
      success: true,
      data: {
        fileName,
        filePath,
        downloadURL,
        size: upload.size,
        type: upload.mimetype,
      },
    });
  } catch (err: any) {
    console.error('Upload error:', err);

    if (err instanceof ValidationError) {
      return createErrorResponse(res, err.message, err.statusCode, origin);
    }
    return createErrorResponse(
      res,
      err instanceof Error ? err.message : 'Upload failed',
      500,
      origin,
    );
  }
}

/* -------------------------------------------------------------------- */
/*                            local helpers                             */
/* -------------------------------------------------------------------- */

function parseMultipart(
  req: NextApiRequest,
): Promise<{ fields: Record<string, any>; files: Record<string, FormidableFile> }> {
  return new Promise((resolve, reject) => {
    const form = new IncomingForm({
      multiples: false,
      maxFileSize: 20 * 1024 * 1024, // 20 MB
    });
    form.parse(req, (err, fields, files) => {
      if (err) reject(err);
      else resolve({ fields, files });
    });
  });
}
