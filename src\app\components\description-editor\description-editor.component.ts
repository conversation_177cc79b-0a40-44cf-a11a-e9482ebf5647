import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PodcastDescription } from '../../models/podcast-description.model';

@Component({
  selector: 'app-description-editor',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="flex flex-col h-full">
      <!-- Header -->
      <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <div>
          <h2 class="text-lg font-semibold text-gray-900">YouTube Description</h2>
          <p class="text-sm text-gray-600">Live preview of your generated description</p>
          <div *ngIf="description" class="text-xs text-gray-500 mt-1">
            Last updated: {{ description.lastModified | date:'medium' }} • Version {{ description.version }}
          </div>
        </div>
        
        <!-- Actions -->
        <div class="flex space-x-2">
          <button (click)="copyDescription()" 
                  [disabled]="!description"
                  class="btn-secondary flex items-center space-x-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>
              <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"/>
            </svg>
            <span>Copy</span>
          </button>
          
          <div class="relative">
            <button (click)="toggleExportMenu()" 
                    [disabled]="!description"
                    class="btn-primary flex items-center space-x-2">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
              <span>Export</span>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>
            
            <!-- Export Dropdown -->
            <div *ngIf="showExportMenu" 
                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
              <button (click)="exportAs('txt')" 
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Export as TXT
              </button>
              <button (click)="exportAs('md')" 
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Export as Markdown
              </button>
              <button (click)="exportAs('json')" 
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Export as JSON
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Content -->
      <div class="flex-1 overflow-y-auto">
        <!-- Empty State -->
        <div *ngIf="!description" class="flex flex-col items-center justify-center h-full text-center py-12">
          <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Description Yet</h3>
          <p class="text-gray-600 mb-4">Upload an audio file or transcript to get started</p>
          <div class="text-sm text-gray-500">
            The AI will automatically generate a comprehensive YouTube description including:
            <ul class="mt-2 space-y-1 text-left">
              <li>• Overview and summary</li>
              <li>• Guest and host bios</li>
              <li>• Key topics and timestamps</li>
              <li>• Resources and links</li>
              <li>• SEO keywords and hashtags</li>
            </ul>
          </div>
        </div>
        
        <!-- Description Content -->
        <div *ngIf="description" class="p-6 space-y-6">
          <!-- Overview -->
          <section class="description-section" *ngIf="description.overview">
            <div class="section-header">
              <h3 class="section-title">Overview</h3>
              <span class="section-badge">{{ getWordCount(description.overview) }} words</span>
            </div>
            <div class="section-content">
              <p class="text-gray-700 leading-relaxed">{{ description.overview }}</p>
            </div>
          </section>
          
          <!-- Guest Bio -->
          <section class="description-section" *ngIf="description.guestBio">
            <div class="section-header">
              <h3 class="section-title">Guest Bio</h3>
              <span class="section-badge">{{ getWordCount(description.guestBio) }} words</span>
            </div>
            <div class="section-content">
              <p class="text-gray-700 leading-relaxed">{{ description.guestBio }}</p>
            </div>
          </section>
          
          <!-- Host Bio -->
          <section class="description-section" *ngIf="description.hostBio">
            <div class="section-header">
              <h3 class="section-title">Host Bio</h3>
              <span class="section-badge">{{ getWordCount(description.hostBio) }} words</span>
            </div>
            <div class="section-content">
              <p class="text-gray-700 leading-relaxed">{{ description.hostBio }}</p>
            </div>
          </section>
          
          <!-- Key Topics -->
          <section class="description-section" *ngIf="description.keyTopics?.length">
            <div class="section-header">
              <h3 class="section-title">Key Topics</h3>
              <span class="section-badge">{{ description.keyTopics.length }} topics</span>
            </div>
            <div class="section-content">
              <div class="grid grid-cols-1 gap-2">
                <div *ngFor="let topic of description.keyTopics; let i = index" 
                     class="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                    {{ i + 1 }}
                  </span>
                  <span class="text-gray-700">{{ topic }}</span>
                </div>
              </div>
            </div>
          </section>
          
          <!-- Resources & Links -->
          <section class="description-section" *ngIf="description.resources?.length">
            <div class="section-header">
              <h3 class="section-title">Resources & Links</h3>
              <span class="section-badge">{{ description.resources.length }} links</span>
            </div>
            <div class="section-content">
              <div class="space-y-3">
                <div *ngFor="let resource of description.resources" 
                     class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                  <!-- Resource Type Icon -->
                  <div class="flex-shrink-0">
                    <svg *ngIf="resource.type === 'website'" class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clip-rule="evenodd"/>
                    </svg>
                    <svg *ngIf="resource.type === 'book'" class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V4.804z"/>
                    </svg>
                    <svg *ngIf="resource.type === 'tool'" class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                    </svg>
                    <svg *ngIf="resource.type === 'social'" class="w-5 h-5 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                    </svg>
                    <svg *ngIf="resource.type === 'product'" class="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM6 9.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm2.5 2.5a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  
                  <div class="flex-1 min-w-0">
                    <a [href]="resource.url" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-blue-600 hover:text-blue-800 font-medium truncate block">
                      {{ resource.label }}
                    </a>
                    <div class="flex items-center space-x-2 mt-1">
                      <span class="text-xs text-gray-500 capitalize px-2 py-1 bg-gray-100 rounded-full">
                        {{ resource.type }}
                      </span>
                      <span *ngIf="resource.confidence" 
                            class="text-xs text-gray-500">
                        {{ (resource.confidence * 100) | number:'1.0-0' }}% confidence
                      </span>
                    </div>
                    <p *ngIf="resource.extractedFrom" 
                       class="text-xs text-gray-400 mt-1 truncate">
                      From: "{{ resource.extractedFrom }}"
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          <!-- Timestamps -->
          <section class="description-section" *ngIf="description.timestamps?.length">
            <div class="section-header">
              <h3 class="section-title">Timestamps</h3>
              <span class="section-badge">{{ description.timestamps.length }} timestamps</span>
            </div>
            <div class="section-content">
              <div class="space-y-2">
                <div *ngFor="let timestamp of description.timestamps" 
                     class="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span class="flex-shrink-0 text-blue-600 font-mono text-sm font-medium bg-blue-50 px-2 py-1 rounded">
                    {{ timestamp.time }}
                  </span>
                  <div class="flex-1">
                    <span class="text-gray-700">{{ timestamp.label }}</span>
                    <div *ngIf="timestamp.confidence" class="text-xs text-gray-400 mt-1">
                      {{ (timestamp.confidence * 100) | number:'1.0-0' }}% confidence
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          <!-- Extended Summary -->
          <section class="description-section" *ngIf="description.extended">
            <div class="section-header">
              <h3 class="section-title">Extended Summary</h3>
              <span class="section-badge">{{ getWordCount(description.extended) }} words</span>
            </div>
            <div class="section-content">
              <p class="text-gray-700 leading-relaxed">{{ description.extended }}</p>
            </div>
          </section>
          
          <!-- SEO & Tags -->
          <section class="description-section" *ngIf="description.seo">
            <div class="section-header">
              <h3 class="section-title">SEO & Tags</h3>
              <span class="section-badge">
                {{ getSeoItemCount(description.seo) }} items
              </span>
            </div>
            <div class="section-content space-y-4">
              <!-- Suggested Title -->
              <div *ngIf="description.seo.suggestedTitle">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Suggested Title</h4>
                <p class="text-gray-700 font-medium">{{ description.seo.suggestedTitle }}</p>
              </div>
              
              <!-- Keywords -->
              <div *ngIf="description.seo.keywords?.length">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Keywords</h4>
                <div class="flex flex-wrap gap-2">
                  <span *ngFor="let keyword of description.seo.keywords"
                        class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {{ keyword }}
                  </span>
                </div>
              </div>
              
              <!-- Hashtags -->
              <div *ngIf="description.seo.hashtags?.length">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Hashtags</h4>
                <div class="flex flex-wrap gap-2">
                  <span *ngFor="let hashtag of description.seo.hashtags"
                        class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                    {{ hashtag }}
                  </span>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .description-section {
      @apply bg-white border border-gray-200 rounded-lg p-4 shadow-sm;
    }
    
    .section-header {
      @apply flex items-center justify-between mb-3;
    }
    
    .section-title {
      @apply text-lg font-semibold text-gray-900;
    }
    
    .section-badge {
      @apply text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full;
    }
    
    .section-content {
      @apply text-gray-700;
    }
    
    .btn-secondary {
      @apply px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
    }
    
    .btn-primary {
      @apply px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
    }
  `]
})
export class DescriptionEditorComponent implements OnChanges {
  @Input() description: PodcastDescription | null = null;
  @Input() isLoading = false;
  
  @Output() copyRequested = new EventEmitter<void>();
  @Output() exportRequested = new EventEmitter<{ format: string; content: string }>();
  
  showExportMenu = false;
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes['description'] && this.description) {
      // Close export menu when description changes
      this.showExportMenu = false;
    }
  }
  
  copyDescription() {
    if (!this.description) return;
    
    const content = this.generateTextContent();
    navigator.clipboard.writeText(content).then(() => {
      this.copyRequested.emit();
    }).catch(err => {
      console.error('Failed to copy description:', err);
    });
  }
  
  toggleExportMenu() {
    this.showExportMenu = !this.showExportMenu;
  }
  
  exportAs(format: 'txt' | 'md' | 'json') {
    if (!this.description) return;
    
    let content: string;
    let filename: string;
    
    switch (format) {
      case 'txt':
        content = this.generateTextContent();
        filename = 'youtube-description.txt';
        break;
      case 'md':
        content = this.generateMarkdownContent();
        filename = 'youtube-description.md';
        break;
      case 'json':
        content = JSON.stringify(this.description, null, 2);
        filename = 'youtube-description.json';
        break;
    }
    
    this.downloadFile(content, filename);
    this.exportRequested.emit({ format, content });
    this.showExportMenu = false;
  }
  
  getWordCount(text: string): number {
    return text ? text.trim().split(/\s+/).length : 0;
  }

  getSeoItemCount(seo: any): number {
    const keywordCount = seo?.keywords?.length || 0;
    const hashtagCount = seo?.hashtags?.length || 0;
    return keywordCount + hashtagCount;
  }
  
  private generateTextContent(): string {
    if (!this.description) return '';
    
    let content = '';
    
    if (this.description.overview) {
      content += `${this.description.overview}\n\n`;
    }
    
    if (this.description.guestBio) {
      content += `GUEST BIO:\n${this.description.guestBio}\n\n`;
    }
    
    if (this.description.hostBio) {
      content += `HOST BIO:\n${this.description.hostBio}\n\n`;
    }
    
    if (this.description.keyTopics?.length) {
      content += `KEY TOPICS:\n`;
      this.description.keyTopics.forEach((topic, index) => {
        content += `${index + 1}. ${topic}\n`;
      });
      content += '\n';
    }
    
    if (this.description.timestamps?.length) {
      content += `TIMESTAMPS:\n`;
      this.description.timestamps.forEach(timestamp => {
        content += `${timestamp.time} - ${timestamp.label}\n`;
      });
      content += '\n';
    }
    
    if (this.description.resources?.length) {
      content += `RESOURCES:\n`;
      this.description.resources.forEach(resource => {
        content += `• ${resource.label}: ${resource.url}\n`;
      });
      content += '\n';
    }
    
    if (this.description.extended) {
      content += `EXTENDED SUMMARY:\n${this.description.extended}\n\n`;
    }
    
    if (this.description.seo?.hashtags?.length) {
      content += `HASHTAGS:\n${this.description.seo.hashtags.join(' ')}\n`;
    }
    
    return content.trim();
  }
  
  private generateMarkdownContent(): string {
    if (!this.description) return '';
    
    let content = '';
    
    if (this.description.seo?.suggestedTitle) {
      content += `# ${this.description.seo.suggestedTitle}\n\n`;
    }
    
    if (this.description.overview) {
      content += `## Overview\n\n${this.description.overview}\n\n`;
    }
    
    if (this.description.guestBio) {
      content += `## Guest Bio\n\n${this.description.guestBio}\n\n`;
    }
    
    if (this.description.hostBio) {
      content += `## Host Bio\n\n${this.description.hostBio}\n\n`;
    }
    
    if (this.description.keyTopics?.length) {
      content += `## Key Topics\n\n`;
      this.description.keyTopics.forEach((topic, index) => {
        content += `${index + 1}. ${topic}\n`;
      });
      content += '\n';
    }
    
    if (this.description.timestamps?.length) {
      content += `## Timestamps\n\n`;
      this.description.timestamps.forEach(timestamp => {
        content += `- **${timestamp.time}** - ${timestamp.label}\n`;
      });
      content += '\n';
    }
    
    if (this.description.resources?.length) {
      content += `## Resources\n\n`;
      this.description.resources.forEach(resource => {
        content += `- [${resource.label}](${resource.url})\n`;
      });
      content += '\n';
    }
    
    if (this.description.extended) {
      content += `## Extended Summary\n\n${this.description.extended}\n\n`;
    }
    
    if (this.description.seo?.hashtags?.length) {
      content += `## Tags\n\n${this.description.seo.hashtags.join(' ')}\n`;
    }
    
    return content.trim();
  }
  
  private downloadFile(content: string, filename: string) {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }
}
