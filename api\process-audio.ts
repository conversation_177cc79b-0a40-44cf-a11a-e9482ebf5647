import { NextApiRequest, NextApiResponse } from 'next';
import {
  verifyAuth,
  verifyOwnership,
  getFirebaseAdmin
} from './lib/utils/auth';
import { MemoryManager } from './lib/utils/memory-manager';
import { CoordinatorAgent } from './lib/agents/coordinator';
import { ProcessingRequest, ValidationError } from './lib/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.status(200).end();
    return;
  }

  const origin = req.headers.origin as string;

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Verify authentication
    const authContext = await verifyAuth(req);

    // Validate request body
    if (!req.body || typeof req.body !== 'object') {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(400).json({ error: 'Invalid request body' });
    }

    const { sessionId, audioFileUrl, userId, autoMode = false } = req.body as ProcessingRequest;

    if (!sessionId || !audioFileUrl || !userId) {
      res.setHeader('Access-Control-Allow-Origin', origin || '*');
      return res.status(400).json({ error: 'Missing required fields: sessionId, audioFileUrl, userId' });
    }

    // Verify user owns this session
    verifyOwnership(authContext, userId);

    // Get Firebase admin instances
    const { firestore } = getFirebaseAdmin();

    // Verify session exists and user owns it
    const sessionDoc = await firestore
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      throw new ValidationError('Session not found');
    }

    const sessionData = sessionDoc.data();
    if (!sessionData) {
      throw new ValidationError('Session data not found');
    }

    // Verify ownership again from session data
    verifyOwnership(authContext, sessionData.userId);

    // Initialize or get agent context
    let agentContext = MemoryManager.getContext(sessionId);
    if (!agentContext) {
      agentContext = MemoryManager.createInitialContext(sessionId, userId);
    }

    // Store audio file URL in memory
    MemoryManager.addMemory(sessionId, 'audioFileUrl', audioFileUrl);

    // Update session status in Firestore
    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update({
        status: 'processing',
        updatedAt: new Date(),
      });

    // Start the coordinator
    const coordinator = CoordinatorAgent.getInstance();
    const result = await coordinator.orchestrateWorkflow(sessionId, undefined, autoMode);

    // Update session with initial result
    const updateData: any = {
      status: result.success ? 'processing' : 'error',
      updatedAt: new Date(),
    };

    if (result.updatedDescription) {
      updateData.description = result.updatedDescription;
    }

    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update(updateData);

    // Prepare response
    const responseData = {
      sessionId,
      status: result.success ? 'processing' : 'error',
      message: result.message,
      nextAgent: result.nextAgent,
      shouldAskUser: result.shouldAskUser,
      agentContext: {
        sessionId: agentContext.sessionId,
        userId: agentContext.userId,
        memoriesCount: Object.keys(agentContext.memories).length,
        agentStatusesCount: Object.keys(agentContext.agentStatuses).length,
      },
      workflowStatus: coordinator.getWorkflowStatus(sessionId),
    };

    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    return res.status(200).json({
      success: result.success,
      data: responseData,
      message: result.message
    });

  } catch (error) {
    console.error('Process audio error:', error);

    res.setHeader('Access-Control-Allow-Origin', origin || '*');
    res.setHeader('Content-Type', 'application/json');

    if (error instanceof Error && 'statusCode' in error) {
      return res.status((error as any).statusCode || 400).json({
        error: error.message
      });
    }

    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Processing failed'
    });
  }
}
