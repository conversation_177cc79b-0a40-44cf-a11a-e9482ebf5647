export interface PodcastDescription {
  guestBio: string;
  hostBio: string;
  overview: string;
  keyTopics: string[];
  resources: ResourceLink[];
  timestamps: Timestamp[];
  extended: string;
  seo: SEOData;
  lastModified: Date;
  version: number;
}

export interface ResourceLink {
  label: string;
  url: string;
  type: 'website' | 'book' | 'tool' | 'social' | 'product';
  confidence: number;
  extractedFrom: string;
}

export interface Timestamp {
  time: string;
  label: string;
  confidence: number;
}

export interface SEOData {
  keywords: string[];
  hashtags: string[];
  suggestedTitle: string;
}

export interface ProcessingSession {
  id: string;
  userId: string;
  audioFileUrl: string;
  transcript: string;
  description: PodcastDescription;
  chatHistory: ChatMessage[];
  status: 'uploading' | 'transcribing' | 'processing' | 'ready' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'system' | 'error';
}

export interface AgentStatus {
  name: string;
  status: 'idle' | 'processing' | 'complete' | 'error';
  progress: number;
  message?: string;
  startTime?: Date;
  endTime?: Date;
}

export interface TranscriptionResult {
  text: string;
  words: WordTimestamp[];
  confidence: number;
  language: string;
  duration: number;
}

export interface WordTimestamp {
  word: string;
  start: number;
  end: number;
  confidence: number;
}

export interface TopicExtractionResult {
  topics: string[];
  entities: Entity[];
  quotes: Quote[];
  keywords: string[];
  sentiment: {
    overall: 'positive' | 'negative' | 'neutral';
    score: number;
  };
}

export interface Entity {
  name: string;
  type: 'person' | 'organization' | 'location' | 'product' | 'other';
  confidence: number;
  mentions: number;
}

export interface Quote {
  text: string;
  speaker?: string;
  timestamp: string;
  confidence: number;
}

export interface ProfileSearchResult {
  name: string;
  platform: 'linkedin' | 'twitter' | 'instagram' | 'website';
  url: string;
  bio: string;
  verified: boolean;
  confidence: number;
  imageUrl?: string;
}

export interface LinkValidationResult {
  url: string;
  isValid: boolean;
  title?: string;
  description?: string;
  type: 'website' | 'book' | 'tool' | 'social' | 'product';
  confidence: number;
}

export interface AgentContext {
  sessionId: string;
  userId: string;
  transcript: string;
  currentDescription: Partial<PodcastDescription>;
  agentStatuses: Record<string, AgentStatus>;
  memory: Record<string, any>;
}

export interface AgentResponse {
  success: boolean;
  data?: any;
  error?: string;
  status: AgentStatus;
  updatedContext?: Partial<AgentContext>;
}

export interface ProcessingRequest {
  sessionId: string;
  audioFileUrl: string;
  userId: string;
  autoMode?: boolean;
}

export interface ChatRequest {
  sessionId: string;
  message: string;
  userId: string;
}

export interface ChatResponse {
  message: string;
  updatedDescription?: Partial<PodcastDescription>;
  agentStatus?: AgentStatus;
}
