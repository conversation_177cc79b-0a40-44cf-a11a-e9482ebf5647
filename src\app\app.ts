import { Component, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import { FirebaseService } from './services/firebase.service';
import { AgentService } from './services/agent.service';
import { FileUploadService, UploadProgress } from './services/file-upload.service';
import { AppErrorHandlerService } from './services/error-handler.service';
import { RetryService } from './services/retry.service';
import { UploadZoneComponent, FileUploadEvent } from './components/upload-zone/upload-zone.component';
import { ChatPanelComponent } from './components/chat-panel/chat-panel.component';
import { DescriptionEditorComponent } from './components/description-editor/description-editor.component';
import { ErrorNotificationsComponent } from './components/error-notifications/error-notifications.component';
import {
  ChatMessage,
  PodcastDescription,
  ProcessingSession,
  User,
  AgentStatus
} from './models/podcast-description.model';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    FormsModule,
    UploadZoneComponent,
    ChatPanelComponent,
    DescriptionEditorComponent,
    ErrorNotificationsComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit, OnDestroy {
  private firebaseService = inject(FirebaseService);
  private agentService = inject(AgentService);
  private fileUploadService = inject(FileUploadService);
  private errorHandler = inject(AppErrorHandlerService);
  private retryService = inject(RetryService);

  // Observable for user authentication state
  user$: Observable<any> = this.firebaseService.user$;

  // Component state
  showUserMenu = false;
  autoMode = false;
  chatInput = '';
  chatMessages: ChatMessage[] = [];
  isProcessing = false;
  currentDescription: PodcastDescription | null = null;
  currentSession: ProcessingSession | null = null;

  // Upload modal state
  showUploadModal = false;
  selectedFile: File | null = null;
  uploadType: 'audio' | 'transcript' = 'audio';
  isUploading = false;
  isDragOver = false;
  uploadProgress: UploadProgress | null = null;
  uploadError: string | null = null;

  // Agent status tracking
  agentStatuses: Record<string, AgentStatus> = {};
  processingProgress = 0;
  estimatedTimeRemaining = 0;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  ngOnInit() {
    // Initialize with welcome message
    this.chatMessages = [
      {
        id: '1',
        role: 'assistant',
        content: 'Hello! I\'m your AI assistant. Upload a podcast audio file or transcript to get started, and I\'ll help you create an amazing YouTube description.',
        timestamp: new Date(),
        type: 'text'
      }
    ];

    // Listen for user authentication changes
    this.subscriptions.push(
      this.user$.subscribe(user => {
        if (user) {
          // User is authenticated - they can start chatting or upload files
          // No need to force upload modal
          console.log('User authenticated:', user.email);
        }
      })
    );

    // Subscribe to agent status updates
    this.subscriptions.push(
      this.agentService.agentStatus$.subscribe(statuses => {
        this.agentStatuses = statuses;
      })
    );

    // Subscribe to upload progress
    this.subscriptions.push(
      this.fileUploadService.getUploadProgress().subscribe(progress => {
        this.uploadProgress = progress;
      })
    );

    // Subscribe to processing progress
    this.subscriptions.push(
      this.agentService.getProgressSummary().subscribe(progress => {
        this.processingProgress = progress.overallProgress;

        // Update chat with progress messages
        if (progress.processingAgents.length > 0) {
          const currentAgent = progress.processingAgents[0];
          this.updateProcessingMessage(currentAgent.name, currentAgent.message, currentAgent.progress);
        }
      })
    );

    // Subscribe to estimated time remaining
    this.subscriptions.push(
      this.agentService.getEstimatedTimeRemaining().subscribe(time => {
        this.estimatedTimeRemaining = time;
      })
    );
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.agentService.reset();
  }

  // Authentication methods
  signInWithGoogle() {
    this.firebaseService.signInWithGoogle().subscribe({
      next: (user) => {
        if (user) {
          console.log('Sign in successful:', user);
        }
      },
      error: (error) => {
        console.error('Sign in failed:', error);
        this.addSystemMessage('Sign in failed. Please try again.');
      }
    });
  }

  async signOut() {
    try {
      await this.firebaseService.signOut();
      this.resetState();
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  }

  // UI methods
  toggleUserMenu() {
    this.showUserMenu = !this.showUserMenu;
  }

  toggleDarkMode() {
    // TODO: Implement dark mode toggle
    console.log('Dark mode toggle clicked');
  }

  // Chat methods
  sendMessage(message?: string) {
    const messageContent = message || this.chatInput.trim();
    if (!messageContent || this.isProcessing) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: messageContent,
      timestamp: new Date(),
      type: 'text'
    };

    this.chatMessages.push(userMessage);
    if (!message) {
      this.chatInput = '';
    }
    this.isProcessing = true;

    // TODO: Send message to AI agent
    this.processUserMessage(messageContent);
  }

  onAutoModeChanged(enabled: boolean) {
    this.autoMode = enabled;
    if (enabled && this.currentSession) {
      // Continue processing in auto mode
      this.continueAutoMode();
    }
  }

  // Chat file upload handlers - SIMPLIFIED
  async onChatFileUploaded(file: File) {
    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        this.errorHandler.handleAuthError(new Error('User not authenticated'));
        return;
      }

      // Create simple local session if none exists
      if (!this.currentSession) {
        this.currentSession = {
          id: 'local-' + Date.now(),
          userId: user.uid,
          audioFileUrl: '',
          transcript: '',
          description: {} as PodcastDescription,
          chatHistory: [],
          status: 'ready',
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }

      // Process file content locally - no upload needed
      try {
        const transcriptContent = await this.processFileLocally(file);
        this.addSystemMessage(`File "${file.name}" processed successfully! Here's the content:`);
        this.sendMessage(`Here's the content from ${file.name}:\n\n${transcriptContent}`);
      } catch (processError) {
        console.error('File processing failed:', processError);
        this.addSystemMessage(`Could not process file "${file.name}". Please try pasting the content directly instead.`);
      }

    } catch (error) {
      console.error('File handling failed:', error);
      this.errorHandler.showError(
        'File Processing Failed',
        error instanceof Error ? error.message : 'Failed to process file',
        { source: 'file-processing' }
      );
    }
  }

  onTranscriptPasted(content: string) {
    // Add system message about pasted content
    this.addSystemMessage('Transcript content pasted. Processing...');

    // Send pasted content as message
    this.sendMessage(`Here's my transcript content:\n\n${content}`);
  }

  // Start chat without file upload - SIMPLIFIED
  async startChatWithoutUpload() {
    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        this.errorHandler.handleAuthError(new Error('User not authenticated'));
        return;
      }

      // Create a simple local session - no Firebase dependency
      this.currentSession = {
        id: 'local-' + Date.now(),
        userId: user.uid,
        audioFileUrl: '',
        transcript: '',
        description: {} as PodcastDescription,
        chatHistory: [],
        status: 'ready',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Close upload modal
      this.closeUploadModal();

      // Add welcome message
      this.addSystemMessage('Chat started! This is a simple demo chat. You can type messages and get responses. File upload is optional.');

      console.log('Simple chat session created:', this.currentSession.id);

    } catch (error) {
      console.error('Failed to start chat:', error);
      this.errorHandler.showError(
        'Failed to Start Chat',
        error instanceof Error ? error.message : 'Could not create chat session',
        { source: 'chat-start' }
      );
    }
  }

  // Create chat session from main interface
  async createChatSession() {
    await this.startChatWithoutUpload();
  }

  // Open upload modal
  openUploadModal() {
    this.showUploadModal = true;
  }

  private async continueAutoMode() {
    if (!this.currentSession) return;

    try {
      const response = await this.agentService.continueAutoMode(this.currentSession.id).toPromise();
      if (response?.success) {
        this.addSystemMessage('Auto mode processing continued.');
      }
    } catch (error) {
      console.error('Error continuing auto mode:', error);
      this.addSystemMessage('Failed to continue auto mode processing.');
    }
  }

  private async processUserMessage(message: string) {
    // Create simple local session if none exists
    if (!this.currentSession) {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        this.errorHandler.handleAuthError(new Error('User not authenticated'));
        this.isProcessing = false;
        return;
      }

      // Create simple local session - no external dependencies
      this.currentSession = {
        id: 'local-' + Date.now(),
        userId: user.uid,
        audioFileUrl: '',
        transcript: '',
        description: {} as PodcastDescription,
        chatHistory: [],
        status: 'ready',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.addSystemMessage('Simple chat session created! Type messages to get responses.');
      console.log('Auto-created local session:', this.currentSession.id);
    }

    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        this.errorHandler.handleAuthError(new Error('User not authenticated'));
        return;
      }

      // Simple local response - no external dependencies
      const responseText = this.generateSimpleResponse(message);

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Add AI response
      const aiResponse: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: responseText,
        timestamp: new Date(),
        type: 'text'
      };

      this.chatMessages.push(aiResponse);

      // Update session with new chat history
      this.currentSession.chatHistory = this.chatMessages;
    } catch (error) {
      console.error('Error processing message:', error);
      this.errorHandler.showError(
        'Chat Error',
        'Failed to process your message. Please try again.',
        {
          source: 'chat',
          action: {
            label: 'Retry',
            handler: () => this.processUserMessage(message)
          }
        }
      );
    } finally {
      this.isProcessing = false;
    }
  }



  // File upload methods
  onFileSelected(event: FileUploadEvent) {
    this.selectedFile = event.file;
    this.uploadType = event.uploadType;
    this.uploadError = null;

    // Show any validation warnings
    if (event.validation.warnings) {
      event.validation.warnings.forEach(warning => {
        this.addSystemMessage(warning, 'warning');
      });
    }
  }

  onUploadTypeChanged(type: 'audio' | 'transcript') {
    this.uploadType = type;
    this.selectedFile = null;
    this.uploadError = null;
  }

  async uploadFile() {
    if (!this.selectedFile) return;

    this.isUploading = true;
    this.uploadError = null;

    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        this.errorHandler.handleAuthError(new Error('User not authenticated'));
        return;
      }

      // Create new session
      const sessionData: Omit<ProcessingSession, 'id'> = {
        userId: user.uid,
        audioFileUrl: '',
        transcript: '',
        description: {} as PodcastDescription,
        chatHistory: [],
        status: 'uploading',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const sessionId = await this.firebaseService.createSession(sessionData);

      // Upload file with retry logic
      const fileUrl = await this.retryService.retryUpload(
        () => this.fileUploadService.uploadFile(
          this.selectedFile!,
          sessionId,
          user.uid,
          this.uploadType
        ),
        this.selectedFile.name
      ).toPromise();

      // Process transcript file if needed
      let transcript = '';
      if (this.uploadType === 'transcript') {
        transcript = await this.fileUploadService.processTranscriptFile(this.selectedFile);
      }

      // Update session with file URL and transcript
      await this.firebaseService.updateSession(sessionId, {
        audioFileUrl: fileUrl,
        transcript: transcript,
        status: this.uploadType === 'transcript' ? 'processing' : 'transcribing'
      });

      this.currentSession = { ...sessionData, id: sessionId, audioFileUrl: fileUrl || '' };

      // Store filename before closing modal
      const fileName = this.selectedFile.name;
      this.closeUploadModal();

      // Show success message
      this.errorHandler.showSuccess(
        'Upload Successful',
        `${this.uploadType === 'audio' ? 'Audio file' : 'Transcript file'} "${fileName}" uploaded successfully`
      );

      // TODO: Start processing pipeline
      this.startProcessingPipeline(sessionId);

    } catch (error) {
      console.error('Upload failed:', error);
      this.errorHandler.handleUploadError(error, this.selectedFile?.name);
    } finally {
      this.isUploading = false;
    }
  }

  private async startProcessingPipeline(sessionId: string) {
    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      this.addSystemMessage('Starting AI processing pipeline...');

      // Get the session data
      const session = await this.firebaseService.getSession(sessionId).toPromise();
      if (!session) {
        throw new Error('Session not found');
      }

      this.currentSession = session;

      // Start processing with agent service
      const response = await this.agentService.startProcessing({
        sessionId,
        audioFileUrl: session.audioFileUrl,
        userId: user.uid,
        autoMode: this.autoMode
      }).toPromise();

      if (response.success) {
        this.addSystemMessage('Processing started! I\'ll update you on progress...');

        // Subscribe to completion
        this.subscriptions.push(
          this.agentService.isProcessingComplete().subscribe(isComplete => {
            if (isComplete) {
              this.onProcessingComplete(sessionId);
            }
          })
        );
      } else {
        this.addSystemMessage(`Processing failed: ${response.message}`);
      }

    } catch (error) {
      console.error('Error starting processing:', error);
      this.addSystemMessage('Failed to start processing. Please try again.');
    }
  }

  private async onProcessingComplete(sessionId: string) {
    try {
      // Get the updated session with results
      const session = await this.firebaseService.getSession(sessionId).toPromise();
      if (session && session.description) {
        this.currentDescription = session.description;
        this.addSystemMessage('Processing complete! Your YouTube description is ready. You can now chat with me to make any changes.');
      }
    } catch (error) {
      console.error('Error getting final results:', error);
      this.addSystemMessage('Processing completed, but there was an error retrieving the results.');
    }
  }

  private updateProcessingMessage(agentName: string, message: string, progress: number) {
    const agentDisplayNames: Record<string, string> = {
      'transcriber': 'Audio Transcription',
      'topicExtractor': 'Topic Analysis',
      'linkFinder': 'Resource Discovery',
      'profileFinder': 'Profile Search',
      'descriptionWriter': 'Description Generation',
      'editor': 'Content Editing'
    };

    const displayName = agentDisplayNames[agentName] || agentName;
    const progressText = progress > 0 ? ` (${Math.round(progress)}%)` : '';

    // Update or add processing message
    const processingMessageId = 'processing-status';
    const existingIndex = this.chatMessages.findIndex(msg => msg.id === processingMessageId);

    const statusMessage: ChatMessage = {
      id: processingMessageId,
      role: 'assistant',
      content: `${displayName}: ${message}${progressText}`,
      timestamp: new Date(),
      type: 'system'
    };

    if (existingIndex >= 0) {
      this.chatMessages[existingIndex] = statusMessage;
    } else {
      this.chatMessages.push(statusMessage);
    }
  }

  private generateSampleDescription() {
    // Sample description for demonstration
    this.currentDescription = {
      guestBio: 'John Doe is a renowned tech entrepreneur and author of "The Future of AI".',
      hostBio: 'Jane Smith hosts the popular Tech Talk podcast, interviewing industry leaders.',
      overview: 'In this episode, we dive deep into the future of artificial intelligence and its impact on society.',
      keyTopics: [
        'The current state of AI technology',
        'Ethical considerations in AI development',
        'Future predictions for AI adoption',
        'Impact on job markets and society'
      ],
      resources: [
        {
          label: 'The Future of AI (Book)',
          url: 'https://example.com/book',
          type: 'book',
          confidence: 0.95,
          extractedFrom: 'Mentioned at 15:30'
        },
        {
          label: 'OpenAI Website',
          url: 'https://openai.com',
          type: 'website',
          confidence: 0.90,
          extractedFrom: 'Discussed at 22:15'
        }
      ],
      timestamps: [
        { time: '00:00', label: 'Introduction and guest welcome', confidence: 0.95 },
        { time: '05:30', label: 'Current state of AI technology', confidence: 0.90 },
        { time: '15:30', label: 'Book discussion: The Future of AI', confidence: 0.92 },
        { time: '25:45', label: 'Ethical considerations in AI', confidence: 0.88 },
        { time: '35:20', label: 'Future predictions and wrap-up', confidence: 0.85 }
      ],
      extended: 'This comprehensive discussion covers the evolution of artificial intelligence from its early days to current breakthroughs. John shares insights from his latest book and discusses the ethical implications of AI development. The conversation explores how AI will reshape industries and what individuals can do to prepare for an AI-driven future.',
      seo: {
        keywords: ['artificial intelligence', 'AI future', 'tech entrepreneur', 'machine learning', 'AI ethics'],
        hashtags: ['#AI', '#TechTalk', '#FutureOfWork', '#MachineLearning', '#Innovation'],
        suggestedTitle: 'The Future of AI: Insights from Tech Entrepreneur John Doe'
      },
      lastModified: new Date(),
      version: 1
    };
  }

  closeUploadModal() {
    this.showUploadModal = false;
    this.selectedFile = null;
    this.isDragOver = false;
  }

  // Description actions
  copyDescription() {
    if (!this.currentDescription) return;

    const formattedDescription = this.formatDescriptionForCopy();
    navigator.clipboard.writeText(formattedDescription).then(() => {
      this.addSystemMessage('Description copied to clipboard!');
    }).catch(() => {
      this.addSystemMessage('Failed to copy description. Please try again.');
    });
  }

  exportDescription() {
    if (!this.currentDescription) return;

    const formattedDescription = this.formatDescriptionForCopy();
    const blob = new Blob([formattedDescription], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'youtube-description.txt';
    a.click();
    window.URL.revokeObjectURL(url);

    this.addSystemMessage('Description exported successfully!');
  }

  private formatDescriptionForCopy(): string {
    if (!this.currentDescription) return '';

    let formatted = '';

    // Overview
    formatted += `${this.currentDescription.overview}\n\n`;

    // Guest Bio
    if (this.currentDescription.guestBio) {
      formatted += `🎤 Guest: ${this.currentDescription.guestBio}\n\n`;
    }

    // Host Bio
    if (this.currentDescription.hostBio) {
      formatted += `🎙️ Host: ${this.currentDescription.hostBio}\n\n`;
    }

    // Key Topics
    if (this.currentDescription.keyTopics?.length) {
      formatted += `📋 Key Topics:\n`;
      this.currentDescription.keyTopics.forEach(topic => {
        formatted += `• ${topic}\n`;
      });
      formatted += '\n';
    }

    // Timestamps
    if (this.currentDescription.timestamps?.length) {
      formatted += `⏰ Timestamps:\n`;
      this.currentDescription.timestamps.forEach(timestamp => {
        formatted += `${timestamp.time} - ${timestamp.label}\n`;
      });
      formatted += '\n';
    }

    // Resources
    if (this.currentDescription.resources?.length) {
      formatted += `🔗 Resources:\n`;
      this.currentDescription.resources.forEach(resource => {
        formatted += `• ${resource.label}: ${resource.url}\n`;
      });
      formatted += '\n';
    }

    // Extended Summary
    if (this.currentDescription.extended) {
      formatted += `📝 Extended Summary:\n${this.currentDescription.extended}\n\n`;
    }

    // SEO
    if (this.currentDescription.seo) {
      if (this.currentDescription.seo.hashtags?.length) {
        formatted += `🏷️ Tags: ${this.currentDescription.seo.hashtags.join(' ')}\n`;
      }
    }

    return formatted;
  }

  private resetState() {
    this.chatMessages = [
      {
        id: '1',
        role: 'assistant',
        content: 'Hello! I\'m your AI assistant. Upload a podcast audio file to get started.',
        timestamp: new Date(),
        type: 'text'
      }
    ];
    this.currentDescription = null;
    this.currentSession = null;
    this.showUserMenu = false;
    this.autoMode = false;
    this.chatInput = '';
    this.isProcessing = false;
  }

  onDescriptionCopied() {
    this.addSystemMessage('Description copied to clipboard!', 'system');
  }

  onDescriptionExported(event: { format: string; content: string }) {
    this.addSystemMessage(`Description exported as ${event.format.toUpperCase()}`, 'system');
  }

  private addSystemMessage(content: string, type: 'text' | 'system' | 'error' | 'warning' = 'system') {
    this.chatMessages.push({
      id: Date.now().toString(),
      role: 'assistant',
      content,
      timestamp: new Date(),
      type
    });
  }

  // Simple response generator - no external dependencies
  generateSimpleResponse(userMessage: string): string {
    const message = userMessage.toLowerCase();

    if (message.includes('hello') || message.includes('hi') || message.includes('hey')) {
      return "Hello! I'm your AI assistant. I'm currently running in simple demo mode. You can chat with me, upload files, or paste transcript content. How can I help you today?";
    }

    if (message.includes('help') || message.includes('what can you do')) {
      return "I can help you with:\n\n• Creating YouTube descriptions from podcast content\n• Processing transcript files\n• Chatting about your content\n• Basic file management\n\nI'm currently in demo mode, so responses are simplified. What would you like to work on?";
    }

    if (message.includes('transcript') || message.includes('content')) {
      return "I can see you're working with transcript content! That's great. I can help you analyze and work with transcript text. You can paste content directly in the chat or upload transcript files. What would you like me to help you with regarding your transcript?";
    }

    if (message.includes('upload') || message.includes('file')) {
      return "You can upload files using the upload button (📎) in the chat area. I support transcript files like .txt, .srt, and .vtt. You can also paste content directly if you prefer. Would you like to try uploading a file or pasting some content?";
    }

    if (message.includes('youtube') || message.includes('description')) {
      return "I can help you create YouTube descriptions! If you have podcast content or transcripts, I can help you turn them into engaging YouTube descriptions. Share your content with me and I'll help you craft something great.";
    }

    // Default response
    return `I understand you're asking about: "${userMessage}"\n\nI'm currently in demo mode, so I can provide basic responses and help with file processing. I can help you work with transcript content, create descriptions, and manage files. What specific task would you like help with?`;
  }

  // Simple local file processing - no external dependencies
  async processFileLocally(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (content) {
          resolve(content);
        } else {
          reject(new Error('Could not read file content'));
        }
      };

      reader.onerror = () => {
        reject(new Error('Error reading file'));
      };

      // Read as text
      reader.readAsText(file);
    });
  }
}
