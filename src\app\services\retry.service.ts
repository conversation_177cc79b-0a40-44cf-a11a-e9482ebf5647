import { Injectable } from '@angular/core';
import { Observable, throwError, timer, of } from 'rxjs';
import { mergeMap, retryWhen, take, concat, delay } from 'rxjs/operators';

export interface RetryConfig {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier?: number;
  maxDelayMs?: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

export interface RetryableOperation<T> {
  operation: () => Observable<T> | Promise<T>;
  config: RetryConfig;
  description: string;
}

@Injectable({
  providedIn: 'root'
})
export class RetryService {
  private readonly DEFAULT_CONFIG: RetryConfig = {
    maxAttempts: 3,
    delayMs: 1000,
    backoffMultiplier: 2,
    maxDelayMs: 10000,
    retryCondition: (error) => this.isRetryableError(error)
  };

  /**
   * Execute an operation with retry logic
   */
  executeWithRetry<T>(operation: RetryableOperation<T>): Observable<T> {
    const config = { ...this.DEFAULT_CONFIG, ...operation.config };
    
    const source$ = this.wrapOperation(operation.operation);
    
    return source$.pipe(
      retryWhen(errors => 
        errors.pipe(
          mergeMap((error, index) => {
            const attempt = index + 1;
            
            // Check if we should retry
            if (attempt >= config.maxAttempts || !config.retryCondition!(error)) {
              return throwError(error);
            }
            
            // Calculate delay with exponential backoff
            const delay = this.calculateDelay(attempt, config);
            
            // Call retry callback if provided
            if (config.onRetry) {
              config.onRetry(attempt, error);
            }
            
            console.log(`Retrying ${operation.description} (attempt ${attempt + 1}/${config.maxAttempts}) after ${delay}ms`);
            
            return timer(delay);
          })
        )
      )
    );
  }

  /**
   * Retry a file upload operation
   */
  retryUpload<T>(
    uploadFn: () => Observable<T> | Promise<T>,
    fileName?: string
  ): Observable<T> {
    return this.executeWithRetry({
      operation: uploadFn,
      description: `upload ${fileName || 'file'}`,
      config: {
        maxAttempts: 3,
        delayMs: 2000,
        backoffMultiplier: 1.5,
        retryCondition: (error) => {
          // Retry on network errors, timeouts, and server errors
          return this.isNetworkError(error) || 
                 this.isTimeoutError(error) || 
                 this.isServerError(error);
        },
        onRetry: (attempt, error) => {
          console.log(`Upload retry attempt ${attempt}: ${error.message}`);
        }
      }
    });
  }

  /**
   * Retry an API call
   */
  retryApiCall<T>(
    apiFn: () => Observable<T> | Promise<T>,
    apiName: string
  ): Observable<T> {
    return this.executeWithRetry({
      operation: apiFn,
      description: `${apiName} API call`,
      config: {
        maxAttempts: 3,
        delayMs: 1000,
        backoffMultiplier: 2,
        maxDelayMs: 8000,
        retryCondition: (error) => {
          // Don't retry on authentication errors or client errors
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          return this.isRetryableError(error);
        },
        onRetry: (attempt, error) => {
          console.log(`${apiName} API retry attempt ${attempt}: ${error.message}`);
        }
      }
    });
  }

  /**
   * Retry a processing operation
   */
  retryProcessing<T>(
    processFn: () => Observable<T> | Promise<T>,
    stage: string
  ): Observable<T> {
    return this.executeWithRetry({
      operation: processFn,
      description: `${stage} processing`,
      config: {
        maxAttempts: 2, // Fewer retries for processing
        delayMs: 3000,
        backoffMultiplier: 1.5,
        retryCondition: (error) => {
          // Only retry on temporary failures
          return this.isTemporaryError(error);
        },
        onRetry: (attempt, error) => {
          console.log(`${stage} processing retry attempt ${attempt}: ${error.message}`);
        }
      }
    });
  }

  /**
   * Retry with exponential backoff
   */
  retryWithBackoff<T>(
    operation: () => Observable<T> | Promise<T>,
    maxAttempts: number = 3,
    initialDelay: number = 1000
  ): Observable<T> {
    return this.executeWithRetry({
      operation,
      description: 'operation',
      config: {
        maxAttempts,
        delayMs: initialDelay,
        backoffMultiplier: 2,
        maxDelayMs: 30000
      }
    });
  }

  /**
   * Create a retryable operation that can be executed later
   */
  createRetryableOperation<T>(
    operation: () => Observable<T> | Promise<T>,
    description: string,
    config?: Partial<RetryConfig>
  ): () => Observable<T> {
    return () => this.executeWithRetry({
      operation,
      description,
      config: { ...this.DEFAULT_CONFIG, ...config }
    });
  }

  private wrapOperation<T>(operation: () => Observable<T> | Promise<T>): Observable<T> {
    try {
      const result = operation();
      if (result instanceof Promise) {
        return new Observable(subscriber => {
          result
            .then(value => {
              subscriber.next(value);
              subscriber.complete();
            })
            .catch(error => subscriber.error(error));
        });
      }
      return result;
    } catch (error) {
      return throwError(error);
    }
  }

  private calculateDelay(attempt: number, config: RetryConfig): number {
    let delay = config.delayMs * Math.pow(config.backoffMultiplier || 1, attempt - 1);
    
    if (config.maxDelayMs) {
      delay = Math.min(delay, config.maxDelayMs);
    }
    
    // Add some jitter to prevent thundering herd
    const jitter = delay * 0.1 * Math.random();
    return Math.floor(delay + jitter);
  }

  private isRetryableError(error: any): boolean {
    return this.isNetworkError(error) || 
           this.isTimeoutError(error) || 
           this.isServerError(error) ||
           this.isTemporaryError(error);
  }

  private isNetworkError(error: any): boolean {
    return error?.name === 'NetworkError' ||
           error?.message?.includes('network') ||
           error?.message?.includes('fetch') ||
           error?.code === 'NETWORK_ERROR';
  }

  private isTimeoutError(error: any): boolean {
    return error?.name === 'TimeoutError' ||
           error?.message?.includes('timeout') ||
           error?.code === 'TIMEOUT';
  }

  private isServerError(error: any): boolean {
    return error?.status >= 500 && error?.status < 600;
  }

  private isTemporaryError(error: any): boolean {
    // Define what constitutes a temporary error that should be retried
    const temporaryMessages = [
      'temporary',
      'temporarily unavailable',
      'service unavailable',
      'try again',
      'rate limit',
      'quota exceeded'
    ];
    
    const message = error?.message?.toLowerCase() || '';
    return temporaryMessages.some(msg => message.includes(msg));
  }

  /**
   * Utility method to check if an error is worth retrying
   */
  shouldRetry(error: any, attempt: number, maxAttempts: number): boolean {
    if (attempt >= maxAttempts) {
      return false;
    }
    
    return this.isRetryableError(error);
  }

  /**
   * Get a human-readable retry message
   */
  getRetryMessage(attempt: number, maxAttempts: number, operation: string): string {
    if (attempt === 1) {
      return `Retrying ${operation}...`;
    } else if (attempt < maxAttempts) {
      return `Retrying ${operation} (attempt ${attempt}/${maxAttempts})...`;
    } else {
      return `Final attempt for ${operation}...`;
    }
  }
}
