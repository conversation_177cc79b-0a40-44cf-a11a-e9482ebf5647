{"hosting": {"public": "dist/pedma-app", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, "functions": {"source": "functions", "runtime": "nodejs20", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8082}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}