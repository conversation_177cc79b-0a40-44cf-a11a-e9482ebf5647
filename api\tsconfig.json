{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}