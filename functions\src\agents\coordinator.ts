import { AgentContext, AgentResponse, AgentStatus } from '../types';
import { LangChainConfig } from '../utils/langchain-config';
import { MemoryManager } from '../utils/memory-manager';

const COORDINATOR_PROMPT = `You are the Coordinator Agent for a podcast-to-YouTube-description system.
Your role is to orchestrate other agents and manage the overall workflow.

Current context: {context}
User request: {input}

Decide which agents to activate and in what order. Respond with a JSON object containing:
- nextAgent: string (name of next agent to run)
- reasoning: string (why this agent should run next)
- parameters: object (parameters to pass to the agent)
- shouldAskUser: boolean (whether to ask user for input)`;

const COORDINATOR_SYSTEM_MESSAGE = "You are an intelligent workflow coordinator. Always respond with valid JSON.";

export class CoordinatorAgent {
  private static instance: CoordinatorAgent;

  static getInstance(): CoordinatorAgent {
    if (!this.instance) {
      this.instance = new CoordinatorAgent();
    }
    return this.instance;
  }

  async orchestrateWorkflow(
    sessionId: string,
    userInput?: string,
    autoMode: boolean = false
  ): Promise<AgentResponse> {
    const startTime = new Date();
    
    try {
      // Update agent status
      const status: AgentStatus = {
        name: 'coordinator',
        status: 'processing',
        progress: 0,
        message: 'Analyzing workflow requirements...',
        startTime
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);

      const context = MemoryManager.getContext(sessionId);
      if (!context) {
        throw new Error(`No context found for session ${sessionId}`);
      }

      // Determine next steps based on current state
      const nextAction = await this.determineNextAction(context, userInput, autoMode);
      
      // Update progress
      status.progress = 50;
      status.message = `Determined next action: ${nextAction.nextAgent}`;
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);

      // Execute the determined action
      const result = await this.executeAction(context, nextAction);

      // Complete
      status.status = 'complete';
      status.progress = 100;
      status.message = `Workflow step completed: ${nextAction.nextAgent}`;
      status.endTime = new Date();
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);

      return {
        success: true,
        data: result,
        status,
        updatedContext: context
      };

    } catch (error) {
      const errorStatus: AgentStatus = {
        name: 'coordinator',
        status: 'error',
        progress: 0,
        message: `Error: ${error}`,
        startTime,
        endTime: new Date()
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'coordinator', errorStatus);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        status: errorStatus
      };
    }
  }

  private async determineNextAction(
    context: AgentContext,
    userInput?: string,
    autoMode: boolean = false
  ): Promise<any> {
    const contextSummary = MemoryManager.getContextSummary(context.sessionId);
    
    const prompt = COORDINATOR_PROMPT
      .replace('{context}', JSON.stringify(contextSummary))
      .replace('{input}', userInput || 'Continue processing');

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      COORDINATOR_SYSTEM_MESSAGE
    );

    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback logic if JSON parsing fails
      return this.getFallbackAction(context, userInput, autoMode);
    }
  }

  private getFallbackAction(context: AgentContext, userInput?: string, autoMode: boolean = false): any {
    const { agentStatuses, transcript, currentDescription } = context;

    // If no transcript, start with transcription
    if (!transcript) {
      return {
        nextAgent: 'transcriber',
        reasoning: 'No transcript available, need to process audio',
        parameters: {},
        shouldAskUser: false
      };
    }

    // If transcript exists but no topics extracted
    if (!agentStatuses.topicExtractor || agentStatuses.topicExtractor.status !== 'complete') {
      return {
        nextAgent: 'topicExtractor',
        reasoning: 'Transcript available, need to extract topics',
        parameters: { transcript },
        shouldAskUser: false
      };
    }

    // If topics extracted but no links found
    if (!agentStatuses.linkFinder || agentStatuses.linkFinder.status !== 'complete') {
      return {
        nextAgent: 'linkFinder',
        reasoning: 'Topics extracted, need to find relevant links',
        parameters: { transcript },
        shouldAskUser: false
      };
    }

    // If links found but no profiles searched
    if (!agentStatuses.profileFinder || agentStatuses.profileFinder.status !== 'complete') {
      return {
        nextAgent: 'profileFinder',
        reasoning: 'Links found, need to search for participant profiles',
        parameters: { transcript },
        shouldAskUser: false
      };
    }

    // If profiles found but no description written
    if (!agentStatuses.descriptionWriter || agentStatuses.descriptionWriter.status !== 'complete') {
      return {
        nextAgent: 'descriptionWriter',
        reasoning: 'All data gathered, ready to write description',
        parameters: { 
          transcript,
          topics: context.memory.topics,
          resources: context.memory.resources,
          profiles: context.memory.profiles
        },
        shouldAskUser: false
      };
    }

    // If user provided input, use editor
    if (userInput) {
      return {
        nextAgent: 'editor',
        reasoning: 'User requested changes to description',
        parameters: { 
          description: currentDescription,
          userRequest: userInput
        },
        shouldAskUser: false
      };
    }

    // All done
    return {
      nextAgent: 'complete',
      reasoning: 'All processing steps completed',
      parameters: {},
      shouldAskUser: !autoMode
    };
  }

  private async executeAction(context: AgentContext, action: any): Promise<any> {
    const { nextAgent, parameters } = action;

    switch (nextAgent) {
      case 'transcriber':
        const { TranscriberAgent } = await import('./transcriber.js');
        return await TranscriberAgent.getInstance().processAudio(context.sessionId, parameters);

      case 'topicExtractor':
        const { TopicExtractorAgent } = await import('./topic-extractor.js');
        return await TopicExtractorAgent.getInstance().extractTopics(context.sessionId, parameters);

      case 'linkFinder':
        const { LinkFinderAgent } = await import('./link-finder.js');
        return await LinkFinderAgent.getInstance().findLinks(context.sessionId, parameters);

      case 'profileFinder':
        const { ProfileFinderAgent } = await import('./profile-finder.js');
        return await ProfileFinderAgent.getInstance().findProfiles(context.sessionId, parameters);

      case 'descriptionWriter':
        const { DescriptionWriterAgent } = await import('./description-writer.js');
        return await DescriptionWriterAgent.getInstance().writeDescription(context.sessionId, parameters);

      case 'editor':
        const { EditorAgent } = await import('./editor.js');
        return await EditorAgent.getInstance().editDescription(context.sessionId, parameters);

      case 'complete':
        return {
          message: 'Processing completed successfully',
          description: context.currentDescription
        };

      default:
        throw new Error(`Unknown agent: ${nextAgent}`);
    }
  }

  async handleUserMessage(sessionId: string, message: string): Promise<AgentResponse> {
    return await this.orchestrateWorkflow(sessionId, message, false);
  }

  async continueAutoMode(sessionId: string): Promise<AgentResponse> {
    return await this.orchestrateWorkflow(sessionId, undefined, true);
  }
}
