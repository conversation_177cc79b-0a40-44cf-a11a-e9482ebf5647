{"name": "pedma-ai-functions", "version": "1.0.0", "description": "Firebase Functions for Pedma AI - Podcast to YouTube Description Generator", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest"}, "engines": {"node": "20"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0", "langchain": "^0.3.0", "@langchain/groq": "^0.1.0", "@langchain/community": "^0.3.0", "groq-sdk": "^0.7.0", "@deepgram/sdk": "^3.4.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "express": "^4.18.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "zod": "^3.22.0"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/multer": "^1.4.0", "@types/uuid": "^9.0.0", "@types/node": "^20.0.0", "typescript": "^5.3.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0"}, "private": true}