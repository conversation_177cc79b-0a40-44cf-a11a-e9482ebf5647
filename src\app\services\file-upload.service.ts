import { Injectable, inject } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { Auth } from '@angular/fire/auth';
import { FirebaseService } from './firebase.service';

export interface UploadProgress {
  progress: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  message: string;
  bytesTransferred?: number;
  totalBytes?: number;
}

export interface FileValidationResult {
  valid: boolean;
  error?: string;
  warnings?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class FileUploadService {
  private auth = inject(Auth);
  private firebaseService = inject(FirebaseService);
  private uploadProgress = new BehaviorSubject<UploadProgress>({
    progress: 0,
    status: 'uploading',
    message: 'Initializing upload...'
  });

  // Configuration
  private readonly MAX_AUDIO_SIZE = 100 * 1024 * 1024; // 100MB
  private readonly MAX_TRANSCRIPT_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly ALLOWED_AUDIO_TYPES = [
    'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/mp4', 'audio/m4a', 
    'audio/aac', 'audio/ogg', 'audio/webm'
  ];
  private readonly ALLOWED_TRANSCRIPT_TYPES = [
    'text/plain', 'text/vtt', 'application/x-subrip'
  ];
  private readonly ALLOWED_TRANSCRIPT_EXTENSIONS = ['.txt', '.srt', '.vtt'];

  /**
   * Validate uploaded file based on type and constraints
   */
  validateFile(file: File, uploadType: 'audio' | 'transcript'): FileValidationResult {
    const warnings: string[] = [];
    
    // Check file size
    const maxSize = uploadType === 'audio' ? this.MAX_AUDIO_SIZE : this.MAX_TRANSCRIPT_SIZE;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds ${maxSize / (1024 * 1024)}MB limit. Current size: ${(file.size / (1024 * 1024)).toFixed(1)}MB`
      };
    }

    // Check file type
    if (uploadType === 'audio') {
      if (!this.ALLOWED_AUDIO_TYPES.includes(file.type)) {
        // Check by extension as fallback
        const extension = file.name.toLowerCase().split('.').pop();
        const allowedExtensions = ['mp3', 'wav', 'mp4', 'm4a', 'aac', 'ogg', 'webm'];
        if (!extension || !allowedExtensions.includes(extension)) {
          return {
            valid: false,
            error: `Unsupported audio format. Supported formats: ${allowedExtensions.join(', ')}`
          };
        } else {
          warnings.push('File type detection may be inaccurate. Proceeding based on file extension.');
        }
      }
    } else {
      // Transcript file validation
      const isValidType = this.ALLOWED_TRANSCRIPT_TYPES.includes(file.type);
      const extension = '.' + file.name.toLowerCase().split('.').pop();
      const isValidExtension = this.ALLOWED_TRANSCRIPT_EXTENSIONS.includes(extension);
      
      if (!isValidType && !isValidExtension) {
        return {
          valid: false,
          error: 'Unsupported transcript format. Supported formats: TXT, SRT, VTT'
        };
      }
    }

    // Additional validations
    if (file.size === 0) {
      return {
        valid: false,
        error: 'File appears to be empty'
      };
    }

    // Warn about very large files
    if (uploadType === 'audio' && file.size > 50 * 1024 * 1024) {
      warnings.push('Large file detected. Upload and processing may take longer.');
    }

    return {
      valid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }



  /**
   * Upload file with progress tracking via Vercel API
   */
  async uploadFile(
    file: File,
    sessionId: string,
    userId: string,
    uploadType: 'audio' | 'transcript'
  ): Promise<string> {
    // Validate file first
    const validation = this.validateFile(file, uploadType);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // Reset progress
    this.uploadProgress.next({
      progress: 0,
      status: 'uploading',
      message: 'Starting upload...'
    });

    try {
      // Check authentication
      const user = this.auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Update progress
      this.uploadProgress.next({
        progress: 25,
        status: 'uploading',
        message: 'Uploading to Firebase Storage...'
      });

      // Use Firebase service for direct upload
      let downloadURL: string;
      if (uploadType === 'transcript') {
        downloadURL = await this.firebaseService.uploadTranscriptFile(file, sessionId, userId);
      } else {
        downloadURL = await this.firebaseService.uploadAudioFile(file, sessionId, userId);
      }

      // Update progress
      this.uploadProgress.next({
        progress: 100,
        status: 'complete',
        message: 'Upload completed successfully!'
      });

      return downloadURL;

    } catch (error) {
      console.error('File upload failed:', error);
      this.uploadProgress.next({
        progress: 0,
        status: 'error',
        message: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });

      // For transcript files, we can still process the content locally
      if (uploadType === 'transcript') {
        console.log('Attempting to process transcript file locally as fallback');
        try {
          const content = await this.processTranscriptFile(file);
          this.uploadProgress.next({
            progress: 100,
            status: 'complete',
            message: 'File processed locally (upload failed but content extracted)'
          });
          // Return a mock URL indicating local processing
          return `local://transcript/${file.name}`;
        } catch (processError) {
          console.error('Local processing also failed:', processError);
        }
      }

      throw error;
    }
  }

  /**
   * Process transcript file content
   */
  async processTranscriptFile(file: File): Promise<string> {
    try {
      const text = await this.readFileAsText(file);
      const extension = file.name.toLowerCase().split('.').pop();

      switch (extension) {
        case 'srt':
          return this.parseSRTFile(text);
        case 'vtt':
          return this.parseVTTFile(text);
        case 'txt':
        default:
          return text.trim();
      }
    } catch (error) {
      throw new Error(`Failed to process transcript file: ${error}`);
    }
  }

  /**
   * Get upload progress observable
   */
  getUploadProgress(): Observable<UploadProgress> {
    return this.uploadProgress.asObservable();
  }

  /**
   * Reset upload progress
   */
  resetProgress(): void {
    this.uploadProgress.next({
      progress: 0,
      status: 'uploading',
      message: 'Ready to upload'
    });
  }

  /**
   * Read file as text
   */
  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(reader.error);
      reader.readAsText(file);
    });
  }

  /**
   * Parse SRT subtitle file format
   */
  private parseSRTFile(content: string): string {
    const lines = content.split('\n');
    const textLines: string[] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      // Skip sequence numbers and timestamps
      if (line && 
          !line.match(/^\d+$/) && 
          !line.match(/\d{2}:\d{2}:\d{2}[,\.]\d{3}\s*-->\s*\d{2}:\d{2}:\d{2}[,\.]\d{3}/)) {
        textLines.push(line);
      }
    }
    
    return textLines.join(' ').replace(/\s+/g, ' ').trim();
  }

  /**
   * Parse VTT subtitle file format
   */
  private parseVTTFile(content: string): string {
    const lines = content.split('\n');
    const textLines: string[] = [];
    let skipHeader = true;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Skip VTT header
      if (skipHeader && line === 'WEBVTT') {
        continue;
      }
      skipHeader = false;
      
      // Skip timestamps and cue identifiers
      if (line && 
          !line.match(/^\d+$/) &&
          !line.match(/\d{2}:\d{2}:\d{2}\.\d{3}\s*-->\s*\d{2}:\d{2}:\d{2}\.\d{3}/) &&
          !line.startsWith('NOTE ') &&
          !line.startsWith('STYLE ')) {
        textLines.push(line);
      }
    }
    
    return textLines.join(' ').replace(/\s+/g, ' ').trim();
  }

  /**
   * Get file type information
   */
  getFileTypeInfo(file: File): { type: 'audio' | 'transcript', format: string } {
    const extension = file.name.toLowerCase().split('.').pop() || '';
    
    if (['mp3', 'wav', 'mp4', 'm4a', 'aac', 'ogg', 'webm'].includes(extension)) {
      return { type: 'audio', format: extension.toUpperCase() };
    } else if (['txt', 'srt', 'vtt'].includes(extension)) {
      return { type: 'transcript', format: extension.toUpperCase() };
    }
    
    // Fallback to MIME type
    if (file.type.startsWith('audio/')) {
      return { type: 'audio', format: file.type.split('/')[1].toUpperCase() };
    } else if (file.type.startsWith('text/')) {
      return { type: 'transcript', format: 'TEXT' };
    }
    
    return { type: 'audio', format: 'UNKNOWN' };
  }
}
