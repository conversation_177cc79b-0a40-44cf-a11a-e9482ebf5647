import { Component, EventEmitter, Input, Output, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { FileUploadService, UploadProgress, FileValidationResult } from '../../services/file-upload.service';

export interface FileUploadEvent {
  file: File;
  uploadType: 'audio' | 'transcript';
  validation: FileValidationResult;
}

@Component({
  selector: 'app-upload-zone',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="space-y-6">
      <!-- Upload Type Selection -->
      <div class="flex items-center justify-center space-x-8">
        <label class="flex items-center space-x-3 cursor-pointer">
          <input type="radio" 
                 [(ngModel)]="uploadType" 
                 value="audio" 
                 name="uploadType" 
                 (change)="onUploadTypeChange()"
                 class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z"/>
            </svg>
            <span class="text-sm font-medium text-gray-700">Audio File</span>
          </div>
        </label>
        
        <label class="flex items-center space-x-3 cursor-pointer">
          <input type="radio" 
                 [(ngModel)]="uploadType" 
                 value="transcript" 
                 name="uploadType" 
                 (change)="onUploadTypeChange()"
                 class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm font-medium text-gray-700">Transcript File</span>
          </div>
        </label>
      </div>

      <!-- Upload Zone -->
      <div class="border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200"
           [class.border-blue-500]="isDragOver"
           [class.bg-blue-50]="isDragOver"
           [class.border-gray-300]="!isDragOver"
           [class.border-red-300]="validationError"
           [class.bg-red-50]="validationError"
           (dragover)="onDragOver($event)"
           (dragleave)="onDragLeave($event)"
           (drop)="onDrop($event)"
           (click)="fileInput.click()">
        
        <!-- Upload Icon -->
        <div class="mb-4">
          <svg *ngIf="uploadType === 'audio'" 
               class="w-12 h-12 text-gray-400 mx-auto" 
               fill="currentColor" viewBox="0 0 20 20">
            <path d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z"/>
          </svg>
          <svg *ngIf="uploadType === 'transcript'" 
               class="w-12 h-12 text-gray-400 mx-auto" 
               fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
          </svg>
        </div>
        
        <!-- Upload Text -->
        <div class="space-y-2">
          <p class="text-lg text-gray-600">
            Drag and drop your {{ uploadType }} file here
          </p>
          <p class="text-sm text-gray-500">or click to browse</p>
          
          <!-- Format Info -->
          <div class="text-xs text-gray-500 mt-3">
            <div *ngIf="uploadType === 'audio'">
              <strong>Supported formats:</strong> MP3, WAV, M4A, MP4, AAC, OGG, WebM<br>
              <strong>Maximum size:</strong> 100MB
            </div>
            <div *ngIf="uploadType === 'transcript'">
              <strong>Supported formats:</strong> TXT, SRT, VTT<br>
              <strong>Maximum size:</strong> 10MB
            </div>
          </div>
        </div>
        
        <!-- Hidden File Input -->
        <input #fileInput
               type="file" 
               [accept]="acceptedTypes"
               (change)="onFileSelected($event)"
               class="hidden">
      </div>
      
      <!-- Validation Error -->
      <div *ngIf="validationError" 
           class="p-3 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-start space-x-2">
          <svg class="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
          <div>
            <p class="text-sm font-medium text-red-800">Upload Error</p>
            <p class="text-sm text-red-700">{{ validationError }}</p>
          </div>
        </div>
      </div>
      
      <!-- Validation Warnings -->
      <div *ngIf="validationWarnings && validationWarnings.length > 0" 
           class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="flex items-start space-x-2">
          <svg class="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
          <div>
            <p class="text-sm font-medium text-yellow-800">Warnings</p>
            <ul class="text-sm text-yellow-700 list-disc list-inside">
              <li *ngFor="let warning of validationWarnings">{{ warning }}</li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- Selected File Info -->
      <div *ngIf="selectedFile && !validationError" 
           class="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div class="flex items-start space-x-3">
          <!-- File Icon -->
          <div class="flex-shrink-0">
            <svg *ngIf="fileTypeInfo?.type === 'audio'" 
                 class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z"/>
            </svg>
            <svg *ngIf="fileTypeInfo?.type === 'transcript'" 
                 class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
            </svg>
          </div>
          
          <!-- File Details -->
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">{{ selectedFile.name }}</p>
            <div class="flex items-center space-x-4 text-xs text-gray-500 mt-1">
              <span>{{ (selectedFile.size / 1024 / 1024) | number:'1.1-1' }} MB</span>
              <span *ngIf="fileTypeInfo">{{ fileTypeInfo.format }}</span>
              <span class="capitalize">{{ fileTypeInfo?.type }}</span>
            </div>
          </div>
          
          <!-- Remove Button -->
          <button (click)="clearFile()" 
                  class="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Upload Progress -->
      <div *ngIf="uploadProgress && uploadProgress.progress > 0" 
           class="space-y-2">
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-600">{{ uploadProgress.message }}</span>
          <span class="text-gray-500">{{ uploadProgress.progress | number:'1.0-0' }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
               [style.width.%]="uploadProgress.progress">
          </div>
        </div>
        <div *ngIf="uploadProgress.bytesTransferred && uploadProgress.totalBytes" 
             class="text-xs text-gray-500 text-center">
          {{ (uploadProgress.bytesTransferred / 1024 / 1024) | number:'1.1-1' }} MB / 
          {{ (uploadProgress.totalBytes / 1024 / 1024) | number:'1.1-1' }} MB
        </div>
      </div>
    </div>
  `
})
export class UploadZoneComponent implements OnInit, OnDestroy {
  @Input() uploadType: 'audio' | 'transcript' = 'audio';
  @Input() disabled = false;
  
  @Output() fileSelected = new EventEmitter<FileUploadEvent>();
  @Output() uploadTypeChanged = new EventEmitter<'audio' | 'transcript'>();
  
  private fileUploadService = inject(FileUploadService);
  private subscriptions: Subscription[] = [];
  
  selectedFile: File | null = null;
  isDragOver = false;
  validationError: string | null = null;
  validationWarnings: string[] | null = null;
  uploadProgress: UploadProgress | null = null;
  fileTypeInfo: { type: 'audio' | 'transcript', format: string } | null = null;
  
  get acceptedTypes(): string {
    return this.uploadType === 'audio' 
      ? 'audio/*,.mp3,.wav,.mp4,.m4a,.aac,.ogg,.webm' 
      : '.txt,.srt,.vtt,text/plain';
  }
  
  ngOnInit() {
    // Subscribe to upload progress
    this.subscriptions.push(
      this.fileUploadService.getUploadProgress().subscribe(progress => {
        this.uploadProgress = progress;
      })
    );
  }
  
  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  
  onUploadTypeChange() {
    this.clearFile();
    this.uploadTypeChanged.emit(this.uploadType);
  }
  
  onDragOver(event: DragEvent) {
    if (this.disabled) return;
    event.preventDefault();
    this.isDragOver = true;
  }
  
  onDragLeave(event: DragEvent) {
    if (this.disabled) return;
    event.preventDefault();
    this.isDragOver = false;
  }
  
  onDrop(event: DragEvent) {
    if (this.disabled) return;
    event.preventDefault();
    this.isDragOver = false;
    
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFile(files[0]);
    }
  }
  
  onFileSelected(event: any) {
    if (this.disabled) return;
    const file = event.target.files[0];
    if (file) {
      this.handleFile(file);
    }
  }
  
  private handleFile(file: File) {
    this.clearValidation();
    
    // Validate file
    const validation = this.fileUploadService.validateFile(file, this.uploadType);
    
    if (!validation.valid) {
      this.validationError = validation.error || 'Invalid file';
      return;
    }
    
    // Set warnings if any
    this.validationWarnings = validation.warnings || null;
    
    // Set file info
    this.selectedFile = file;
    this.fileTypeInfo = this.fileUploadService.getFileTypeInfo(file);
    
    // Emit file selected event
    this.fileSelected.emit({
      file,
      uploadType: this.uploadType,
      validation
    });
  }
  
  clearFile() {
    this.selectedFile = null;
    this.fileTypeInfo = null;
    this.clearValidation();
    this.fileUploadService.resetProgress();
  }
  
  private clearValidation() {
    this.validationError = null;
    this.validationWarnings = null;
  }
}
