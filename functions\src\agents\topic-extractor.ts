import { AgentContext, AgentResponse, AgentStatus, TopicExtractionResult } from '../types';
import { LangChainConfig } from '../utils/langchain-config';
import { MemoryManager } from '../utils/memory-manager';

export class TopicExtractorAgent {
  private static instance: TopicExtractorAgent;

  static getInstance(): TopicExtractorAgent {
    if (!this.instance) {
      this.instance = new TopicExtractorAgent();
    }
    return this.instance;
  }

  async extractTopics(sessionId: string, parameters: any): Promise<AgentResponse> {
    const startTime = new Date();
    
    try {
      const status: AgentStatus = {
        name: 'topicExtractor',
        status: 'processing',
        progress: 0,
        message: 'Analyzing transcript for topics...',
        startTime
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'topicExtractor', status);

      const context = MemoryManager.getContext(sessionId);
      if (!context) {
        throw new Error(`No context found for session ${sessionId}`);
      }

      const transcript = parameters.transcript || context.transcript;
      if (!transcript) {
        throw new Error('No transcript available for topic extraction');
      }

      // Update progress
      status.progress = 20;
      status.message = 'Extracting main topics...';
      MemoryManager.updateAgentStatus(sessionId, 'topicExtractor', status);

      // Extract main topics
      const topics = await this.extractMainTopics(transcript, context);

      // Update progress
      status.progress = 40;
      status.message = 'Identifying entities and quotes...';
      MemoryManager.updateAgentStatus(sessionId, 'topicExtractor', status);

      // Extract entities and quotes
      const entities = await this.extractEntities(transcript, context);
      const quotes = await this.extractQuotes(transcript, context);

      // Update progress
      status.progress = 60;
      status.message = 'Generating SEO keywords...';
      MemoryManager.updateAgentStatus(sessionId, 'topicExtractor', status);

      // Extract SEO keywords
      const keywords = await this.extractSEOKeywords(transcript, topics, context);

      // Update progress
      status.progress = 80;
      status.message = 'Analyzing sentiment...';
      MemoryManager.updateAgentStatus(sessionId, 'topicExtractor', status);

      // Analyze sentiment
      const sentiment = await this.analyzeSentiment(transcript, context);

      // Compile results
      const extractionResult: TopicExtractionResult = {
        topics,
        entities,
        quotes,
        keywords,
        sentiment
      };

      // Store results in memory
      MemoryManager.addMemory(sessionId, 'topics', extractionResult);

      // Complete
      status.status = 'complete';
      status.progress = 100;
      status.message = `Topic extraction completed: ${topics.length} topics, ${entities.length} entities`;
      status.endTime = new Date();
      MemoryManager.updateAgentStatus(sessionId, 'topicExtractor', status);

      const updatedContext = MemoryManager.getContext(sessionId);
      return {
        success: true,
        data: extractionResult,
        status,
        updatedContext: updatedContext || undefined
      };

    } catch (error) {
      const errorStatus: AgentStatus = {
        name: 'topicExtractor',
        status: 'error',
        progress: 0,
        message: `Topic extraction failed: ${error}`,
        startTime,
        endTime: new Date()
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'topicExtractor', errorStatus);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown topic extraction error',
        status: errorStatus
      };
    }
  }

  private async extractMainTopics(transcript: string, context: AgentContext): Promise<string[]> {
    const prompt = `Analyze this podcast transcript and extract 5-8 main topics discussed:

Transcript: ${transcript.substring(0, 4000)}...

Extract the main topics as a JSON array of strings. Focus on:
- Key subjects discussed
- Main themes and concepts
- Important discussions points
- Educational content areas

Respond with only a JSON array of topic strings.`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an expert content analyst. Respond only with valid JSON array of strings."
    );

    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback: extract topics manually
      return this.fallbackTopicExtraction(transcript);
    }
  }

  private async extractEntities(transcript: string, context: AgentContext): Promise<any[]> {
    const prompt = `Extract named entities from this podcast transcript:

Transcript: ${transcript.substring(0, 4000)}...

Find and categorize entities like:
- People (guests, hosts, mentioned individuals)
- Organizations (companies, institutions)
- Products (tools, books, software)
- Locations (cities, countries)

Respond with JSON array of objects with: name, type, confidence, mentions`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an expert at named entity recognition. Respond only with valid JSON."
    );

    try {
      return JSON.parse(response);
    } catch (error) {
      return [];
    }
  }

  private async extractQuotes(transcript: string, context: AgentContext): Promise<any[]> {
    const prompt = `Extract 3-5 most impactful quotes from this podcast transcript:

Transcript: ${transcript.substring(0, 4000)}...

Find quotes that are:
- Insightful or thought-provoking
- Memorable or quotable
- Represent key insights
- Would be good for social media

Respond with JSON array of objects with: text, speaker, timestamp, confidence`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an expert at identifying impactful quotes. Respond only with valid JSON."
    );

    try {
      return JSON.parse(response);
    } catch (error) {
      return [];
    }
  }

  private async extractSEOKeywords(transcript: string, topics: string[], context: AgentContext): Promise<string[]> {
    const prompt = `Generate SEO keywords for this podcast content:

Topics: ${topics.join(', ')}
Transcript sample: ${transcript.substring(0, 2000)}...

Generate 10-15 SEO keywords that would help this content be discovered on YouTube. Include:
- Primary topic keywords
- Long-tail keywords
- Industry-specific terms
- Popular search terms

Respond with JSON array of keyword strings.`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an SEO expert. Respond only with valid JSON array of strings."
    );

    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback: generate basic keywords from topics
      return topics.flatMap(topic => topic.toLowerCase().split(' '));
    }
  }

  private async analyzeSentiment(transcript: string, context: AgentContext): Promise<any> {
    const prompt = `Analyze the overall sentiment of this podcast:

Transcript sample: ${transcript.substring(0, 3000)}...

Determine:
- Overall sentiment (positive, negative, neutral)
- Sentiment score (-1 to 1)
- Key emotional themes

Respond with JSON object: {overall: string, score: number, themes: string[]}`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an expert at sentiment analysis. Respond only with valid JSON."
    );

    try {
      return JSON.parse(response);
    } catch (error) {
      return {
        overall: 'neutral',
        score: 0,
        themes: []
      };
    }
  }

  private fallbackTopicExtraction(transcript: string): string[] {
    // Simple keyword-based topic extraction as fallback
    const commonTopics = [
      'technology', 'business', 'entrepreneurship', 'innovation', 'leadership',
      'marketing', 'productivity', 'growth', 'strategy', 'success',
      'artificial intelligence', 'machine learning', 'software', 'startup'
    ];

    const transcriptLower = transcript.toLowerCase();
    const foundTopics = commonTopics.filter(topic => 
      transcriptLower.includes(topic)
    );

    return foundTopics.slice(0, 6);
  }
}
