import { AgentContext, AgentStatus } from '../types';

export class MemoryManager {
  private static contexts: Map<string, AgentContext> = new Map();

  static getContext(sessionId: string): AgentContext | null {
    const context = this.contexts.get(sessionId) || null;
    if (context) {
      context.lastAccessed = new Date();
      this.contexts.set(sessionId, context);
    }
    return context;
  }

  static setContext(sessionId: string, context: AgentContext): void {
    context.lastAccessed = new Date();
    this.contexts.set(sessionId, context);
  }

  static updateContext(sessionId: string, updates: Partial<AgentContext>): AgentContext {
    const existing = this.getContext(sessionId);
    if (!existing) {
      throw new Error(`No context found for session ${sessionId}`);
    }

    const updated = {
      ...existing,
      ...updates,
      memories: {
        ...existing.memories,
        ...updates.memories
      },
      agentStatuses: {
        ...existing.agentStatuses,
        ...updates.agentStatuses
      },
      lastAccessed: new Date()
    };

    this.setContext(sessionId, updated);
    return updated;
  }

  static updateAgentStatus(sessionId: string, agentName: string, status: AgentStatus): void {
    const context = this.getContext(sessionId);
    if (context) {
      context.agentStatuses[agentName] = {
        ...status,
        lastUpdated: new Date()
      };
      this.setContext(sessionId, context);
    }
  }

  static getAgentStatus(sessionId: string, agentName: string): AgentStatus | null {
    const context = this.getContext(sessionId);
    return context?.agentStatuses[agentName] || null;
  }

  static addMemory(sessionId: string, key: string, value: any): void {
    const context = this.getContext(sessionId);
    if (context) {
      context.memories[key] = value;
      this.setContext(sessionId, context);
    }
  }

  static getMemory(sessionId: string, key: string): any {
    const context = this.getContext(sessionId);
    return context?.memories[key];
  }

  static clearContext(sessionId: string): void {
    this.contexts.delete(sessionId);
  }

  static createInitialContext(sessionId: string, userId: string): AgentContext {
    const context: AgentContext = {
      sessionId,
      userId,
      memories: {},
      agentStatuses: {},
      createdAt: new Date(),
      lastAccessed: new Date()
    };

    this.setContext(sessionId, context);
    return context;
  }

  static getAllActiveSessions(): string[] {
    return Array.from(this.contexts.keys());
  }

  static getContextSummary(sessionId: string): any {
    const context = this.getContext(sessionId);
    if (!context) return null;

    return {
      sessionId: context.sessionId,
      userId: context.userId,
      memoriesCount: Object.keys(context.memories).length,
      activeAgents: Object.keys(context.agentStatuses).filter(
        agent => ['thinking', 'working'].includes(context.agentStatuses[agent].status)
      ),
      completedAgents: Object.keys(context.agentStatuses).filter(
        agent => context.agentStatuses[agent].status === 'completed'
      ),
      memoryKeys: Object.keys(context.memories),
      createdAt: context.createdAt,
      lastAccessed: context.lastAccessed
    };
  }

  static exportContext(sessionId: string): AgentContext | null {
    return this.getContext(sessionId);
  }

  static importContext(sessionId: string, context: AgentContext): void {
    this.setContext(sessionId, context);
  }

  // Cleanup old contexts (call periodically)
  static cleanup(maxAgeHours: number = 24): number {
    const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
    let cleaned = 0;

    for (const [sessionId, context] of this.contexts.entries()) {
      // Check if context is older than cutoff
      const lastActivity = Math.max(
        context.lastAccessed.getTime(),
        ...Object.values(context.agentStatuses)
          .map(status => status.lastUpdated.getTime())
      );

      if (lastActivity < cutoffTime) {
        this.contexts.delete(sessionId);
        cleaned++;
      }
    }

    return cleaned;
  }

  // Get memory usage statistics
  static getStats(): {
    totalSessions: number;
    totalMemorySize: number;
    oldestSession: Date | null;
    newestSession: Date | null;
  } {
    const sessions = Array.from(this.contexts.values());
    
    return {
      totalSessions: sessions.length,
      totalMemorySize: JSON.stringify(sessions).length,
      oldestSession: sessions.length > 0 
        ? new Date(Math.min(...sessions.map(s => s.createdAt.getTime())))
        : null,
      newestSession: sessions.length > 0
        ? new Date(Math.max(...sessions.map(s => s.createdAt.getTime())))
        : null
    };
  }
}
